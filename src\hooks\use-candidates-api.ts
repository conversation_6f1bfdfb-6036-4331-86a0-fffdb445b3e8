import { useState, useEffect, useCallback } from 'react';
import { ApiService, convertApiCandidateToLocal, type ApiResponse } from '@/services/api';
import { type Candidate } from '@/types/candidate';

interface UseCandidatesApiProps {
  userId: string;
  userType: string;
  userName: string;
  enabled?: boolean;
}

interface UseCandidatesApiReturn {
  candidates: Candidate[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  apiResponse: ApiResponse | null;
}

export function useCandidatesApi({
  userId,
  userType,
  userName,
  enabled = true,
}: UseCandidatesApiProps): UseCandidatesApiReturn {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null);

  const fetchCandidates = useCallback(async () => {
    if (!enabled || !userId || !userType || !userName) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await ApiService.fetchCandidates(
        userId,
        userType,
        userName,
        1 // For now, always fetch page 1
      );

      setApiResponse(response);
      
      // Convert API candidates to local format
      const convertedCandidates = response.candidates.map(convertApiCandidateToLocal);
      setCandidates(convertedCandidates);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch candidates';
      setError(errorMessage);
      console.error('Failed to fetch candidates:', err);
    } finally {
      setLoading(false);
    }
  }, [userId, userType, userName, enabled]);

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    fetchCandidates();
  }, [fetchCandidates]);

  return {
    candidates,
    loading,
    error,
    refetch: fetchCandidates,
    apiResponse,
  };
}
