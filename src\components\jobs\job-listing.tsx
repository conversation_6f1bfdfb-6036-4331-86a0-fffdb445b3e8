import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Search,
  FileText,
  UserPlus,
  Edit,
  Trash2,
  Calendar,
  ChevronDown,
  X,
  Check,
} from "lucide-react";
import {
  KebabMenu,
  createKebabMenuItem,
  type KebabMenuItem,
} from "@/components/ui/kebab-menu";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { TableSkeleton } from "@/components/ui/table-skeleton";
import { useDebounce } from "@/hooks/use-debounce";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { useUser } from "@/contexts/user-context";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchJobs,
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setDateFilter,
  setCustomDateRange,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  type Job,
  type FilterTag,
} from "@/store/slices/jobsSlice";
import {
  selectJobsLoading,
  selectJobsError,
  selectSearchTags,
  selectDateFilter,
  selectAppliedCustomDateRange,
  selectCurrentPage,
  selectItemsPerPage,
  selectSortConfig,
  selectFilteredJobs,
  selectPaginatedJobs,
  selectSearchSuggestions,
  columns,
} from "@/store/selectors/jobsSelectors";

// Type definitions and constants are now imported from Redux store

// --- PARENT COMPONENT ---
export function JobListing() {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 flex-1 w-full">
        <JobListingTable />
      </div>
    </div>
  );
}

// --- MAIN TABLE COMPONENT ---
function JobListingTable() {
  const { userEmail, userName } = useUser();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Redux state
  const loading = useAppSelector(selectJobsLoading);
  const error = useAppSelector(selectJobsError);
  const searchTags = useAppSelector(selectSearchTags);

  const dateFilter = useAppSelector(selectDateFilter);
  const appliedCustomDateRange = useAppSelector(selectAppliedCustomDateRange);
  const currentPage = useAppSelector(selectCurrentPage);
  const itemsPerPage = useAppSelector(selectItemsPerPage);
  const sortConfig = useAppSelector(selectSortConfig);
  const filteredJobs = useAppSelector(selectFilteredJobs);
  const currentJobs = useAppSelector(selectPaginatedJobs);

  // Local UI state
  const [inputValue, setInputValue] = useState("");
  const debouncedInputValue = useDebounce(inputValue, 250);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState<string>("");
  const [customEndDate, setCustomEndDate] = useState<string>("");

  // Refs
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const dateDropdownRef = useRef<HTMLDivElement>(null);
  const dateButtonRef = useRef<HTMLButtonElement>(null);

  // Get search suggestions from Redux
  const suggestions = useAppSelector((state) =>
    selectSearchSuggestions(state, debouncedInputValue)
  );

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Fetch jobs on component mount
  useEffect(() => {
    const username = userName || userEmail || "managerone";
    dispatch(fetchJobs({ username }));
  }, [dispatch, userName, userEmail]);

  // useEffects for UI interactions (closing dropdowns)
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node)
      ) {
        setInputValue(""); // Clear input instead of suggestions since suggestions come from Redux
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [searchContainerRef]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dateDropdownRef.current &&
        !dateDropdownRef.current.contains(event.target as Node) &&
        dateButtonRef.current &&
        !dateButtonRef.current.contains(event.target as Node)
      ) {
        setIsDateDropdownOpen(false);
      }
    }
    if (isDateDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDateDropdownOpen]);

  // --- HANDLER FUNCTIONS ---
  const handleAddTag = (
    tagOrSuggestion: string | { value: string; column: string }
  ) => {
    const newTag: FilterTag =
      typeof tagOrSuggestion === "string"
        ? { value: tagOrSuggestion.trim(), column: "Any" }
        : {
            value: (tagOrSuggestion.value || "").trim(),
            column: tagOrSuggestion.column,
          };
    if (
      newTag.value &&
      !searchTags.some(
        (t) => t.value === newTag.value && t.column === newTag.column
      )
    ) {
      dispatch(addSearchTag(newTag));
    }
    setInputValue("");
  };
  const handleRemoveTag = (tagToRemove: FilterTag) => {
    dispatch(removeSearchTag(tagToRemove));
  };
  const handleApplyFilters = () => {
    dispatch(applyFilters());
  };
  const handleClearAllFilters = () => {
    dispatch(clearAllFilters());
    setInputValue("");
    setCustomStartDate("");
    setCustomEndDate("");
  };
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };
  const dateFilterOptions = [
    { value: null, label: "All Time" },
    { value: 1, label: "Last 24 Hours" },
    { value: 7, label: "Last 7 Days" },
    { value: 30, label: "Last 30 Days" },
  ];
  const handleDateFilter = (days: number | null) => {
    dispatch(setDateFilter(days));
    setIsDateDropdownOpen(false);
  };
  const handleCustomDateApply = () => {
    if (customStartDate && customEndDate) {
      dispatch(
        setCustomDateRange({ start: customStartDate, end: customEndDate })
      );
      setIsDateDropdownOpen(false);
    } else {
      alert("Please select both start and end dates");
    }
  };
  const getCurrentDateFilterLabel = () => {
    if (appliedCustomDateRange) {
      const startDate = new Date(
        appliedCustomDateRange.start
      ).toLocaleDateString();
      const endDate = new Date(appliedCustomDateRange.end).toLocaleDateString();
      return `${startDate} - ${endDate}`;
    }
    const option = dateFilterOptions.find((opt) => opt.value === dateFilter);
    return option ? option.label : "Select Date";
  };
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    dispatch(setCurrentPage(pageNumber));
  };
  const handleSort = async (key: keyof Job | "posted_by") => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }
    dispatch(setSortConfig({ key, direction }));
  };
  const getStatusColor = (status: Job["job_status"]) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Closed":
        return "bg-red-100 text-red-800";
      case "On Hold":
        return "bg-yellow-100 text-yellow-800";
      case "Hold":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };
  const createJobMenuItems = (job: Job): KebabMenuItem[] => [
    createKebabMenuItem(
      "add-candidate",
      "Add Candidate",
      () => {
        // Navigate to register candidate with job details
        const currentPath = location.pathname;
        const basePath = currentPath.includes("/manager/")
          ? "/manager"
          : "/recruiter";
        navigate(`${basePath}/register-candidate/${job.id}`, {
          state: {
            jobId: job.id,
            client: job.client,
            role: job.role,
            jobStatus: job.job_status,
          },
        });
      },
      { icon: UserPlus }
    ),
    createKebabMenuItem(
      "view-jd",
      "View Job Description",
      () => alert(`View JD for job ${job.id}`),
      { icon: FileText, separator: true }
    ),
    createKebabMenuItem(
      "edit-job",
      "Edit Job",
      () => alert(`Edit job ${job.id}`),
      { icon: Edit }
    ),
    createKebabMenuItem(
      "delete-job",
      "Delete Job",
      () => {
        if (window.confirm(`Are you sure you want to delete job ${job.id}?`)) {
          alert(`Delete job ${job.id}`);
        }
      },
      { icon: Trash2, variant: "destructive" }
    ),
  ];

  return (
    <div className="flex flex-col">
      {error && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-800">
          <strong>API Connection Issue:</strong> {error}. Showing stale or empty
          data.
        </div>
      )}
      <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
        <div className="flex-1 min-w-[300px] relative" ref={searchContainerRef}>
          <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500">
            <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
            {searchTags.map((tag) => (
              <span
                key={`${tag.column}-${tag.value}`}
                className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                <span className="font-normal text-blue-600 mr-1">
                  {tag.column}:
                </span>
                {tag.value}
                <button
                  onClick={() => handleRemoveTag(tag)}
                  className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"
                >
                  <X className="h-3.5 w-3.5" />
                </button>
              </span>
            ))}
            <input
              type="text"
              className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none"
              placeholder="Search and add filters..."
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={(e) => {
                if (e.key === "Enter" && inputValue.trim()) {
                  e.preventDefault();
                  handleAddTag(inputValue);
                }
              }}
            />
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
            <button
              onClick={handleApplyFilters}
              title="Apply Filters"
              className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600"
            >
              <Check className="h-5 w-5" />
            </button>
            <button
              onClick={handleClearAllFilters}
              title="Clear All Filters"
              className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          {suggestions.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div
                  key={`${s.value}-${i}`}
                  className="cursor-pointer p-3 hover:bg-blue-50 flex justify-between items-center text-sm"
                  onClick={() => handleAddTag(s)}
                >
                  <span className="font-medium text-gray-900">{s.value}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md font-medium">
                    {s.column}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
        <div className="flex flex-wrap items-center gap-3">
          <div className="relative">
            <button
              ref={dateButtonRef}
              onClick={() => setIsDateDropdownOpen(!isDateDropdownOpen)}
              className="bg-white border border-gray-300 text-gray-900 px-3 py-2.5 rounded-md text-sm font-medium flex items-center gap-2 hover:bg-gray-50 min-w-[140px] justify-between"
            >
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{getCurrentDateFilterLabel()}</span>
              </div>
              <ChevronDown
                className={`h-4 w-4 transition-transform ${
                  isDateDropdownOpen ? "rotate-180" : ""
                }`}
              />
            </button>
            {isDateDropdownOpen && (
              <div
                ref={dateDropdownRef}
                className="absolute right-0 top-full mt-1 z-50 bg-white rounded-lg shadow-xl border border-gray-200 min-w-[220px] py-2"
              >
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b">
                  Filter by Date
                </div>
                {dateFilterOptions.map((option) => (
                  <button
                    key={option.label}
                    onClick={() => handleDateFilter(option.value)}
                    className={`w-full text-left px-3 py-2 text-sm flex justify-between items-center hover:bg-gray-100 ${
                      dateFilter === option.value
                        ? "font-bold text-blue-600"
                        : ""
                    }`}
                  >
                    {option.label}
                    {dateFilter === option.value && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </button>
                ))}
                <div className="border-t mt-2 pt-2 px-3">
                  <div className="text-xs font-semibold text-gray-500 mb-2">
                    Custom Range
                  </div>
                  <div className="space-y-2">
                    <input
                      type="date"
                      value={customStartDate}
                      onChange={(e) => setCustomStartDate(e.target.value)}
                      className="w-full border border-gray-300 px-2 py-1 rounded text-xs"
                    />
                    <input
                      type="date"
                      value={customEndDate}
                      onChange={(e) => setCustomEndDate(e.target.value)}
                      className="w-full border border-gray-300 px-2 py-1 rounded text-xs"
                    />
                    <button
                      onClick={handleCustomDateApply}
                      disabled={!customStartDate || !customEndDate}
                      className="w-full px-3 py-1.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animatePagination();
              dispatch(setItemsPerPage(Number(e.target.value)));
            }}
            value={itemsPerPage}
          >
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
          </select>
        </div>
      </div>
      <AnimatedTableWrapper
        isLoading={isLoading || loading}
        loadingComponent={<TableSkeleton rows={8} cols={columns.length + 1} />}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div className="overflow-auto h-[485px]">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className="px-2 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                <th
                  scope="col"
                  className="px-2 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td
                    colSpan={columns.length + 1}
                    className="text-center py-4 text-xs text-gray-500"
                  >
                    Loading jobs...
                  </td>
                </tr>
              ) : currentJobs.length > 0 ? (
                currentJobs.map((job, index) => (
                  <AnimatedTableRow key={job.id} index={index}>
                    {/* FIX: No whitespace here */}
                    {columns.map((column) => {
                      const cellClassName =
                        "px-2 py-2 text-xs text-gray-800 font-medium";
                      if (column.key === "job_status") {
                        return (
                          <td
                            key={`${job.id}-status`}
                            className={cellClassName}
                          >
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${getStatusColor(
                                job.job_status
                              )}`}
                            >
                              {job.job_status}
                            </span>
                          </td>
                        );
                      }
                      if (column.key === "posted_by") {
                        return (
                          <td
                            key={`${job.id}-postedby`}
                            className={cellClassName}
                          >
                            <span
                              className="font-semibold text-blue-600 truncate block whitespace-nowrap overflow-hidden"
                              title={job.management}
                            >
                              {job.management}
                            </span>
                          </td>
                        );
                      }
                      if (column.key === "client") {
                        return (
                          <td
                            key={`${job.id}-client`}
                            className={cellClassName}
                          >
                            <span
                              className="font-semibold text-green-700 truncate block whitespace-nowrap overflow-hidden"
                              title={job.client}
                            >
                              {job.client}
                            </span>
                          </td>
                        );
                      }
                      if (column.key === "recruiter") {
                        return (
                          <td
                            key={`${job.id}-recruiter`}
                            className={`${cellClassName} max-w-xs`}
                          >
                            <span
                              className="block truncate"
                              title={job.recruiter}
                            >
                              {job.recruiter}
                            </span>
                          </td>
                        );
                      }
                      return (
                        <td
                          key={`${job.id}-${String(column.key)}`}
                          className={`${cellClassName} whitespace-nowrap `}
                        >
                          {String(job[column.key as keyof Job] || "")}
                        </td>
                      );
                    })}
                    <td className="px-2 py-2 text-xs text-center">
                      <KebabMenu items={createJobMenuItems(job)} />
                    </td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length + 1}
                    className="text-center py-4 text-xs text-gray-500"
                  >
                    No jobs found matching your criteria.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          {filteredJobs.length > 0 ? (
            <>
              Showing{" "}
              {Math.min(
                1 + (currentPage - 1) * itemsPerPage,
                filteredJobs.length
              )}{" "}
              to {Math.min(currentPage * itemsPerPage, filteredJobs.length)} of{" "}
              {filteredJobs.length} jobs
            </>
          ) : (
            "No jobs found"
          )}
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredJobs.length / itemsPerPage)}
          onPageChange={paginate}
        />
      </div>
    </div>
  );
}
