import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUser } from "@/contexts/user-context";
import { useCandidates } from "@/contexts/candidates-context";

export function Logout() {
  const { logout } = useUser();
  const { clearCache } = useCandidates();
  const navigate = useNavigate();

  useEffect(() => {
    // Clear candidates cache
    clearCache();

    // Call the logout function from context
    logout();

    // Redirect to landing page
    navigate("/");
  }, [logout, clearCache, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <svg
            className="animate-spin h-10 w-10 text-blue-600 mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-gray-600">Logging out...</p>
          <p className="text-sm text-gray-500 mt-2">
            You will be redirected to the home page.
          </p>
        </div>
      </div>
    </div>
  );
}
