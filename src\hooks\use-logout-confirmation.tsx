import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useUser } from "@/contexts/user-context";
import { LogoutConfirmationDialog } from "@/components/ui/logout-confirmation-dialog";

export function useLogoutConfirmation() {
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const navigate = useNavigate();
  const { logout } = useUser();

  const handleLogoutClick = () => {
    setShowLogoutDialog(true);
  };

  const handleLogoutConfirm = () => {
    logout();
    navigate("/login");
    setShowLogoutDialog(false);
  };

  const handleLogoutCancel = () => {
    setShowLogoutDialog(false);
  };

  const LogoutDialog = () => (
    <LogoutConfirmationDialog
      isOpen={showLogoutDialog}
      onConfirm={handleLogoutConfirm}
      onCancel={handleLogoutCancel}
    />
  );

  return {
    handleLogout<PERSON>lick,
    LogoutDialog,
  };
}
