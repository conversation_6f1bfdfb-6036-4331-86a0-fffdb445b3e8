Stack trace:
Frame         Function      Args
0007FFFF53E0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF42E0) msys-2.0.dll+0x2118E
0007FFFF53E0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF53E0  0002100469F2 (00021028DF99, 0007FFFF5298, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF53E0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF53E0  00021006A545 (0007FFFF53F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF53F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE71F20000 ntdll.dll
7FFE71CD0000 KERNEL32.DLL
7FFE6F050000 KERNELBASE.dll
7FFE6FF60000 USER32.dll
7FFE6F590000 win32u.dll
000210040000 msys-2.0.dll
7FFE6FE00000 GDI32.dll
7FFE6F450000 gdi32full.dll
7FFE6F9C0000 msvcp_win.dll
7FFE6F650000 ucrtbase.dll
7FFE6FEA0000 advapi32.dll
7FFE702B0000 msvcrt.dll
7FFE70370000 sechost.dll
7FFE71330000 RPCRT4.dll
7FFE6E640000 CRYPTBASE.DLL
7FFE6F920000 bcryptPrimitives.dll
7FFE70DD0000 IMM32.DLL
