import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ApiService, type ActiveUser, type ActiveUsersResponse } from '@/services/api';

// User interface (converted from API format)
export interface User {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  userType: "management" | "recruiter";
  isVerified: boolean;
  isActive: boolean;
  isPeerReviewer: boolean;
}

// Filter tag interface
export interface FilterTag {
  column: string;
  value: string;
}

// Active Users state interface
export interface ActiveUsersState {
  users: User[];
  loading: boolean;
  error: string | null;
  // Filter and search state
  searchTags: FilterTag[];
  appliedTags: FilterTag[];
  // Pagination state
  currentPage: number;
  itemsPerPage: number;
  // Sorting state
  sortConfig: {
    key: keyof User | null;
    direction: "ascending" | "descending" | null;
  };
}

// Initial state
const initialState: ActiveUsersState = {
  users: [],
  loading: false,
  error: null,
  searchTags: [],
  appliedTags: [],
  currentPage: 1,
  itemsPerPage: 10,
  sortConfig: { key: null, direction: null },
};

// Helper function to convert API user to local format
const convertApiUserToLocal = (apiUser: ActiveUser): User => {
  const nameParts = apiUser.name.split(' ');
  return {
    id: apiUser.id,
    username: apiUser.username,
    firstName: nameParts[0] || '',
    lastName: nameParts.slice(1).join(' ') || '',
    email: apiUser.email,
    userType: apiUser.user_type === 'management' ? 'management' : 'recruiter',
    isVerified: apiUser.is_verified,
    isActive: apiUser.is_active,
    isPeerReviewer: apiUser.peer_reviewer_status,
  };
};

// Async thunk for fetching active users
export const fetchActiveUsers = createAsyncThunk(
  'activeUsers/fetchActiveUsers',
  async (_, { rejectWithValue }) => {
    try {
      const response: ActiveUsersResponse = await ApiService.fetchActiveUsers();
      const allActiveUsers = [
        ...response.active_users_manager,
        ...response.active_users_recruiter,
      ];
      return allActiveUsers.map(convertApiUserToLocal);
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "An unknown error occurred");
    }
  }
);

// Async thunk for updating user verification status
export const updateUserVerification = createAsyncThunk(
  'activeUsers/updateUserVerification',
  async ({ username, newStatus }: { username: string; newStatus: boolean }, { rejectWithValue }) => {
    try {
      await ApiService.updateUserStatus(username, newStatus, 'verify');
      return { username, newStatus, field: 'isVerified' as const };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "Failed to update verification status");
    }
  }
);

// Async thunk for updating user active status
export const updateUserActiveStatus = createAsyncThunk(
  'activeUsers/updateUserActiveStatus',
  async ({ username, newStatus }: { username: string; newStatus: boolean }, { rejectWithValue }) => {
    try {
      await ApiService.updateUserStatus(username, newStatus, newStatus ? 'verify' : 'deactivate');
      return { username, newStatus, field: 'isActive' as const };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "Failed to update active status");
    }
  }
);

// Async thunk for updating user peer reviewer status
export const updateUserPeerReviewerStatus = createAsyncThunk(
  'activeUsers/updateUserPeerReviewerStatus',
  async ({ username, newStatus }: { username: string; newStatus: boolean }, { rejectWithValue }) => {
    try {
      await ApiService.updateUserStatus(username, newStatus, 'peer_reviewer');
      return { username, newStatus, field: 'isPeerReviewer' as const };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "Failed to update peer reviewer status");
    }
  }
);

// Active Users slice
const activeUsersSlice = createSlice({
  name: 'activeUsers',
  initialState,
  reducers: {
    // Search and filter actions
    addSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const newTag = action.payload;
      if (!state.searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
        state.searchTags.push(newTag);
      }
    },
    removeSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const tagToRemove = action.payload;
      state.searchTags = state.searchTags.filter(
        tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column
      );
    },
    applyFilters: (state) => {
      state.appliedTags = [...state.searchTags];
      state.currentPage = 1;
    },
    clearAllFilters: (state) => {
      state.searchTags = [];
      state.appliedTags = [];
      state.currentPage = 1;
    },
    // Pagination actions
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
      state.currentPage = 1;
    },
    // Sorting actions
    setSortConfig: (state, action: PayloadAction<{
      key: keyof User | null;
      direction: "ascending" | "descending" | null;
    }>) => {
      state.sortConfig = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch active users
      .addCase(fetchActiveUsers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchActiveUsers.fulfilled, (state, action) => {
        state.loading = false;
        state.users = action.payload;
        state.error = null;
      })
      .addCase(fetchActiveUsers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update user verification
      .addCase(updateUserVerification.fulfilled, (state, action) => {
        const { username, newStatus } = action.payload;
        const userIndex = state.users.findIndex(user => user.username === username);
        if (userIndex !== -1) {
          state.users[userIndex].isVerified = newStatus;
        }
      })
      // Update user active status
      .addCase(updateUserActiveStatus.fulfilled, (state, action) => {
        const { username, newStatus } = action.payload;
        const userIndex = state.users.findIndex(user => user.username === username);
        if (userIndex !== -1) {
          state.users[userIndex].isActive = newStatus;
        }
      })
      // Update user peer reviewer status
      .addCase(updateUserPeerReviewerStatus.fulfilled, (state, action) => {
        const { username, newStatus } = action.payload;
        const userIndex = state.users.findIndex(user => user.username === username);
        if (userIndex !== -1) {
          state.users[userIndex].isPeerReviewer = newStatus;
        }
      });
  },
});

export const {
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
} = activeUsersSlice.actions;

export default activeUsersSlice.reducer;
