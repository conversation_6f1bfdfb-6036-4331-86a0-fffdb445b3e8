import { useEffect } from "react";
import { Navigate, useNavigate } from "react-router-dom";
import { UserRole } from "@/contexts/user-context";
import { MainLayout } from "@/components/layout/main-layout";

// Component to redirect based on user role - now redirects to landing page
export const RoleBasedRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const userRole = localStorage.getItem("userRole") as UserRole;

    // Redirect to landing page for authenticated users
    if (userRole === "manager" || userRole === "recruiter") {
      navigate("/landing", { replace: true });
    } else {
      // Fallback to login if no role is found
      navigate("/login", { replace: true });
    }
  }, [navigate]);

  // Return null as this is just a redirect component
  return null;
};

// Auth guard component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
}

export const ProtectedRoute = ({
  children,
  requiredRoles,
}: ProtectedRouteProps) => {
  const userRole = localStorage.getItem("userRole") as UserRole;
  const isAuthenticated = localStorage.getItem("isAuthenticated") === "true";

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRoles && !requiredRoles.includes(userRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};

// Layout wrapper for authenticated routes
export const AuthenticatedLayout = ({
  children,
  requiredRoles,
}: {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
}) => {
  return (
    <ProtectedRoute requiredRoles={requiredRoles}>
      <MainLayout>{children}</MainLayout>
    </ProtectedRoute>
  );
};
