import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Navigate, RouteObject } from "react-router-dom";
import { Suspense, lazy } from "react";
import PeerAssignedProfiles from "./components/peer-assigned-profiles";
import { LoadingSpinner } from "./components/ui/loading-spinner";
import { LandingLayout } from "./components/layout/landing-layout";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Error<PERSON>allback,
} from "./components/error/error-boundary";
import {
  RoleBasedRedirect,
  ProtectedRoute,
  AuthenticatedLayout,
} from "./components/routing/route-components";

// Implement lazy loading for all components
const Dashboard = lazy(() =>
  import("./components/dashboard/dashboard").then((module) => ({
    default: module.Dashboard,
  }))
);
const Login = lazy(() =>
  import("./components/auth/login").then((module) => ({
    default: module.Login,
  }))
);
const Register = lazy(() =>
  import("./components/auth/register").then((module) => ({
    default: module.Register,
  }))
);
const Logout = lazy(() =>
  import("./components/auth/logout").then((module) => ({
    default: module.Logout,
  }))
);
const ChangePassword = lazy(() =>
  import("./components/auth/change-password").then((module) => ({
    default: module.ChangePassword,
  }))
);
const AssignedRequirements = lazy(() =>
  import("./components/requirements/assigned-requirements").then((module) => ({
    default: module.AssignedRequirements,
  }))
);
const RegisterCandidate = lazy(() =>
  import("./components/candidates/register-candidate").then((module) => ({
    default: module.RegisterCandidate,
  }))
);
const CalendarView = lazy(() => import("./components/calendar/calendar"));
const JobListing = lazy(() =>
  import("./components/jobs/job-listing").then((module) => ({
    default: module.JobListing,
  }))
);
const JobAssignments = lazy(() =>
  import("./components/jobs/job-assignments").then((module) => ({
    default: module.JobAssignments,
  }))
);
const UserAccounts = lazy(() =>
  import("./components/users/user-accounts").then((module) => ({
    default: module.UserAccounts,
  }))
);
const ProfileTransfer = lazy(() =>
  import("./components/profile/profile-transfer").then((module) => ({
    default: module.ProfileTransfer,
  }))
);
const ManagerAnalytics = lazy(() =>
  import("./components/analytics/manager-analytics").then((module) => ({
    default: module.ManagerAnalytics,
  }))
);
const RecruiterAnalytics = lazy(() =>
  import("./components/analytics/recruiter-analytics").then((module) => ({
    default: module.RecruiterAnalytics,
  }))
);
const ProfileAnalysis = lazy(() =>
  import("./components/profile-analysis/profile-analysis").then((module) => ({
    default: module.ProfileAnalysis,
  }))
);
const HelpSupport = lazy(() =>
  import("./components/help-support/help-support").then((module) => ({
    default: module.HelpSupport,
  }))
);
const LandingPage = lazy(() =>
  import("./components/landing/landing-page").then((module) => ({
    default: module.LandingPage,
  }))
);

// Define routes
const routes: RouteObject[] = [
  // Public routes
  {
    path: "/login",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Login />
      </Suspense>
    ),
  },
  {
    path: "/register",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Register />
      </Suspense>
    ),
  },
  {
    path: "/logout",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Logout />
      </Suspense>
    ),
  },

  // Root redirect based on user role
  {
    path: "/",
    element: (
      <ProtectedRoute>
        <Suspense fallback={<LoadingSpinner />}>
          <RoleBasedRedirect />
        </Suspense>
      </ProtectedRoute>
    ),
  },

  // Common routes - Dashboard redirects to role-specific dashboard
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <Suspense fallback={<LoadingSpinner />}>
          <RoleBasedRedirect />
        </Suspense>
      </ProtectedRoute>
    ),
  },
  {
    path: "/change-password",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <ChangePassword />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/calendar",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <CalendarView />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/register-candidate",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/help-and-support",
    element: (
      <AuthenticatedLayout>
        <Suspense fallback={<LoadingSpinner />}>
          <HelpSupport />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/landing",
    element: (
      <ErrorBoundary>
        <ProtectedRoute>
          <LandingLayout>
            <Suspense fallback={<LoadingSpinner />}>
              <LandingPage />
            </Suspense>
          </LandingLayout>
        </ProtectedRoute>
      </ErrorBoundary>
    ),
    errorElement: (
      <ErrorFallback
        error={new Error("Failed to load landing page")}
        resetError={() => window.location.reload()}
      />
    ),
  },

  // Manager routes
  {
    path: "/manager/dashboard",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/job-listing",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobListing />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/job-assignments",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <JobAssignments />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/register-candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/register-candidate/:jobId",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/profile-transfer",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ProfileTransfer />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/analytics",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ManagerAnalytics />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/user-accounts",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <UserAccounts />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/manager/peer-assigned-profiles",
    element: (
      <AuthenticatedLayout requiredRoles={["manager"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <PeerAssignedProfiles />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  // Recruiter routes
  {
    path: "/recruiter/dashboard",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <Dashboard />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/requirements",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <AssignedRequirements />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/register-candidate",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/register-candidate/:jobId",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RegisterCandidate />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  {
    path: "/recruiter/analytics",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <RecruiterAnalytics />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/profile-analysis",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <ProfileAnalysis />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },
  {
    path: "/recruiter/peer-assigned-profiles",
    element: (
      <AuthenticatedLayout requiredRoles={["recruiter"]}>
        <Suspense fallback={<LoadingSpinner />}>
          <PeerAssignedProfiles />
        </Suspense>
      </AuthenticatedLayout>
    ),
  },

  // Catch-all route
  {
    path: "*",
    element: <Navigate to="/dashboard" replace />,
  },
];

// Create router with global error handling
export const router = createBrowserRouter(routes);
