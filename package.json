{"name": "hr-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.4", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.4", "@types/react-calendar": "^4.1.0", "@types/react-image-crop": "^8.1.6", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "framer-motion": "^12.9.2", "lucide-react": "^0.503.0", "react": "^19.0.0", "react-calendar": "^5.1.0", "react-dom": "^19.0.0", "react-image-crop": "^11.0.10", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "recharts": "^3.1.2", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tw-animate-css": "^1.2.8", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}