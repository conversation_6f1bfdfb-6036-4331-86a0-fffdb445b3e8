import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { ApiService, convertApiCandidateToLocal, type ApiResponse } from '@/services/api';
import { type Candidate } from '@/types/candidate';

// Filter tag interface
export interface FilterTag {
  column: string;
  value: string;
}

// Candidates state interface
export interface CandidatesState {
  candidates: Candidate[];
  loading: boolean;
  error: string | null;
  lastFetched: string | null;
  apiResponse: ApiResponse | null;
  // Filter and search state
  searchTags: FilterTag[];
  appliedTags: FilterTag[];
  dateFilter: number | null;
  appliedCustomDateRange: { start: string; end: string } | null;
  // Pagination state
  currentPage: number;
  itemsPerPage: number;
  // Sorting state
  sortConfig: {
    key: keyof Candidate | null;
    direction: "ascending" | "descending" | null;
  };
  // UI state
  selectedRows: number[];
  selectAll: boolean;
  // Column visibility
  visibleColumns: (keyof Candidate)[];
}

// Initial state
const initialState: CandidatesState = {
  candidates: [],
  loading: false,
  error: null,
  lastFetched: null,
  apiResponse: null,
  searchTags: [],
  appliedTags: [],
  dateFilter: null,
  appliedCustomDateRange: null,
  currentPage: 1,
  itemsPerPage: 10,
  sortConfig: { key: null, direction: null },
  selectedRows: [],
  selectAll: false,
  visibleColumns: [
    'appliedDate',
    'lastUpdated',
    'jobId',
    'firstName',
    'email',
    'phone',
    'client',
    'profile',
    'skills',
    'status',
    'comment',
    'peerReviewer',
    'recruiter'
  ],
};

// Async thunk for fetching candidates
export const fetchCandidates = createAsyncThunk(
  'candidates/fetchCandidates',
  async (params: { userId: string; userType: string; userName: string; force?: boolean }, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { candidates: CandidatesState };
      const { lastFetched, candidates } = state.candidates;
      
      // Check cache validity (5 minutes)
      const CACHE_DURATION = 5 * 60 * 1000;
      const isCacheValid = lastFetched && 
        (new Date().getTime() - new Date(lastFetched).getTime()) < CACHE_DURATION;
      
      // Don't fetch if cache is valid and not forced
      if (!params.force && isCacheValid && candidates.length > 0) {
        console.log('Using cached candidates data');
        return { candidates, fromCache: true };
      }

      console.log('Fetching candidates from API...');
      
      const userType = params.userType === "manager" ? "management" : "recruiter";
      const response = await ApiService.fetchCandidates(
        params.userId,
        userType,
        params.userName,
        1 // For now, always fetch page 1
      );

      // Convert API candidates to local format
      const convertedCandidates = response.candidates.map(convertApiCandidateToLocal);
      
      console.log(`Fetched ${convertedCandidates.length} candidates`);
      
      return {
        candidates: convertedCandidates,
        apiResponse: response,
        fromCache: false
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : "An unknown error occurred");
    }
  }
);

// Candidates slice
const candidatesSlice = createSlice({
  name: 'candidates',
  initialState,
  reducers: {
    // Search and filter actions
    addSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const newTag = action.payload;
      if (!state.searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
        state.searchTags.push(newTag);
      }
    },
    removeSearchTag: (state, action: PayloadAction<FilterTag>) => {
      const tagToRemove = action.payload;
      state.searchTags = state.searchTags.filter(
        tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column
      );
    },
    applyFilters: (state) => {
      state.appliedTags = [...state.searchTags];
      state.dateFilter = null;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    clearAllFilters: (state) => {
      state.searchTags = [];
      state.appliedTags = [];
      state.dateFilter = null;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    setDateFilter: (state, action: PayloadAction<number | null>) => {
      state.appliedTags = [];
      state.searchTags = [];
      state.dateFilter = action.payload;
      state.appliedCustomDateRange = null;
      state.currentPage = 1;
    },
    setCustomDateRange: (state, action: PayloadAction<{ start: string; end: string }>) => {
      state.appliedCustomDateRange = action.payload;
      state.dateFilter = null;
      state.appliedTags = [];
      state.searchTags = [];
      state.currentPage = 1;
    },
    // Pagination actions
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
      state.currentPage = 1;
    },
    // Sorting actions
    setSortConfig: (state, action: PayloadAction<{
      key: keyof Candidate | null;
      direction: "ascending" | "descending" | null;
    }>) => {
      state.sortConfig = action.payload;
    },
    // Row selection actions
    toggleRowSelection: (state, action: PayloadAction<number>) => {
      const candidateId = action.payload;
      const index = state.selectedRows.indexOf(candidateId);
      if (index > -1) {
        state.selectedRows.splice(index, 1);
      } else {
        state.selectedRows.push(candidateId);
      }
      state.selectAll = state.selectedRows.length === state.candidates.length;
    },
    setSelectAll: (state, action: PayloadAction<boolean>) => {
      state.selectAll = action.payload;
      if (action.payload) {
        state.selectedRows = state.candidates.map(c => c.id);
      } else {
        state.selectedRows = [];
      }
    },
    clearSelection: (state) => {
      state.selectedRows = [];
      state.selectAll = false;
    },
    // Column visibility actions
    toggleColumnVisibility: (state, action: PayloadAction<keyof Candidate>) => {
      const column = action.payload;
      const index = state.visibleColumns.indexOf(column);
      if (index > -1) {
        state.visibleColumns.splice(index, 1);
      } else {
        state.visibleColumns.push(column);
      }
    },
    setVisibleColumns: (state, action: PayloadAction<(keyof Candidate)[]>) => {
      state.visibleColumns = action.payload;
    },
    // Cache management
    clearCache: (state) => {
      state.candidates = [];
      state.lastFetched = null;
      state.apiResponse = null;
      state.error = null;
    },
    refreshCandidates: (state) => {
      state.lastFetched = null; // This will force a refresh on next fetch
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCandidates.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCandidates.fulfilled, (state, action) => {
        state.loading = false;
        if (!action.payload.fromCache) {
          state.candidates = action.payload.candidates;
          state.apiResponse = action.payload.apiResponse || null;
          state.lastFetched = new Date().toISOString();
        }
        state.error = null;
      })
      .addCase(fetchCandidates.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setDateFilter,
  setCustomDateRange,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  toggleRowSelection,
  setSelectAll,
  clearSelection,
  toggleColumnVisibility,
  setVisibleColumns,
  clearCache,
  refreshCandidates,
} = candidatesSlice.actions;

export default candidatesSlice.reducer;
