import { useState, useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { useUser } from "@/contexts/user-context";
import { useLocation, useParams } from "react-router-dom";
import { fetchJobs } from "@/store/slices/jobsSlice";
import { selectClientCompanies, selectJobs, selectJobsLoading, type ClientCompany } from "@/store/selectors/jobsSelectors";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";

import { selectApiResponse } from "@/store/selectors/candidatesSelectors";
// Helper function to get status-based styling
const getCompanyStatusStyling = (status: ClientCompany['status']) => {
  switch (status) {
    case "Closed":
      return {
        border: "border-red-300",
        background: "bg-red-50",
        hover: "hover:bg-red-100",
        text: "text-red-700",
        badge: "bg-red-100 text-red-800",
      };
    case "Hold":
      return {
        border: "border-yellow-300",
        background: "bg-yellow-50",
        hover: "hover:bg-yellow-100",
        text: "text-yellow-700",
        badge: "bg-yellow-100 text-yellow-800",
      };
    case "Active":
    default:
      return {
        border: "border-gray-200",
        background: "bg-white",
        hover: "hover:bg-gray-50",
        text: "text-gray-700",
        badge: "bg-green-100 text-green-800",
      };
  }
};

// Interface for form data
interface CandidateFormData {
  jobId: string;
  name: string;
  mobile: string;
  email: string;
  client: string;
  profile: string;
  skills: string;
  qualifications: string;
  resume: File | null;
  additionalFiles: File | null;
  // Compensation
  currentCTC: string;
  currentCTCCurrencyType: "INR" | "USD" | "EUR" | "GBP";
  expectedCTC: string;
  expectedCTCCurrencyType: "INR" | "USD" | "EUR" | "GBP";
  // Other details
  reasonForJobChange: string;
  currentCompany: string;
  currentJobPosition: string;
  currentJobLocation: string;
  preferredJobLocation: string;
  totalExperienceYears: string;
  totalExperienceMonths: string;
  relevantExperienceYears: string;
  relevantExperienceMonths: string;
  noticePeriod: string; // Added for Serving Notice Period
  joiningOffer: string; // Added for Holding Offer
  linkedinUrl: string; // Added for LinkedIn URL
  remarks: string; // Added for Remarks
}

export function RegisterCandidate() {
  const dispatch = useAppDispatch();
  const { userName, userEmail } = useUser();
  const location = useLocation();
  const params = useParams();

  // Get job details or candidate from navigation state or URL params
  const jobDetails = location.state as {
    jobId?: string;
    client?: string;
    role?: string;
    jobStatus?: string;
    candidate?: any;
  } | null;

  // If editing, get candidate object
  const apiResponse = useAppSelector(selectApiResponse);

  const candidateToEdit = jobDetails?.candidate;
  const editMode = !!candidateToEdit;

  // Redux selectors
  const clientCompanies = useAppSelector(selectClientCompanies);
  const jobs = useAppSelector(selectJobs);
  const jobsLoading = useAppSelector(selectJobsLoading);

  // State for the selected company
  const [selectedCompany, setSelectedCompany] = useState<string | null>(
    jobDetails?.client || (candidateToEdit ? candidateToEdit.client : null) || null
  );

  // State for search and pagination
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const companiesPerPage = 8; // 2x4 grid

  // Animation hook for pagination
  const { animatePagination } = useTableAnimation();

  // State for form data
  const [formData, setFormData] = useState<CandidateFormData>({
    jobId: candidateToEdit?.jobId || jobDetails?.jobId || params.jobId || "",
    // Ensure full name in edit mode
    name: candidateToEdit ? `${candidateToEdit.firstName || ""} ${candidateToEdit.lastName || ""}`.trim() || candidateToEdit.name || "" : "",
    mobile: candidateToEdit?.mobile || candidateToEdit?.phone || "",
    email: candidateToEdit?.email || "",
    client: candidateToEdit?.client || jobDetails?.client || "",
    profile: candidateToEdit?.profile || jobDetails?.role || "",
    skills: candidateToEdit?.skills || "",
    qualifications: candidateToEdit?.qualifications || "",
    resume: null, // File fields cannot be pre-filled
    additionalFiles: null, // File fields cannot be pre-filled
    // Compensation defaults
    currentCTC: candidateToEdit?.currentCTC || candidateToEdit?.salary || "",
    currentCTCCurrencyType: (candidateToEdit?.currentCTCCurrencyType as any) || "INR",
    expectedCTC: candidateToEdit?.expectedCTC || "",
    expectedCTCCurrencyType: (candidateToEdit?.expectedCTCCurrencyType as any) || "INR",
    // Other details
    reasonForJobChange: candidateToEdit?.reasonForJobChange || "",
    currentCompany: candidateToEdit?.currentCompany || "",
    currentJobPosition: candidateToEdit?.currentJobPosition || "",
    currentJobLocation: candidateToEdit?.currentJobLocation || "",
    preferredJobLocation: candidateToEdit?.preferredJobLocation || "",
    totalExperienceYears: candidateToEdit?.totalExperienceYears || "0",
    totalExperienceMonths: candidateToEdit?.totalExperienceMonths || "0",
    relevantExperienceYears: candidateToEdit?.relevantExperienceYears || "0",
    relevantExperienceMonths: candidateToEdit?.relevantExperienceMonths || "0",
    noticePeriod: candidateToEdit?.noticePeriod || "",
    joiningOffer: candidateToEdit?.joiningOffer || "",
    linkedinUrl: candidateToEdit?.linkedinUrl || "",
    remarks: candidateToEdit?.remarks || "",
  });

  // Fetch jobs data on component mount
  useEffect(() => {
    if (jobs.length === 0) {
      dispatch(fetchJobs({ username: userName || userEmail || "managerone" }));
    }
  }, [dispatch, userName, userEmail, jobs.length]);

  // Update form data when job details or candidateToEdit are available
  useEffect(() => {
    if (candidateToEdit) {
      setFormData(prev => ({
        ...prev,
        ...candidateToEdit,
        name: `${candidateToEdit.firstName || ""} ${candidateToEdit.lastName || ""}`.trim() || candidateToEdit.name || prev.name,
        mobile: candidateToEdit.mobile || candidateToEdit.phone || prev.mobile,
        // keep files empty
        resume: null,
        additionalFiles: null,
        // ensure compensation fields are present (fallbacks)
        currentCTC: (candidateToEdit as any).currentCTC || (candidateToEdit as any).salary || prev.currentCTC || "",
        expectedCTC: (candidateToEdit as any).expectedCTC || prev.expectedCTC || "",
        currentCTCCurrencyType: ((candidateToEdit as any).currentCTCCurrencyType as any) || prev.currentCTCCurrencyType || "INR",
        expectedCTCCurrencyType: ((candidateToEdit as any).expectedCTCCurrencyType as any) || prev.expectedCTCCurrencyType || "INR",
      }));
      if (candidateToEdit.client) {
        setSelectedCompany(candidateToEdit.client);
      }
    } else if (jobDetails) {
      setFormData(prev => ({
        ...prev,
        jobId: jobDetails.jobId || prev.jobId,
        client: jobDetails.client || prev.client,
        profile: jobDetails.role || prev.profile,
      }));
      if (jobDetails.client) {
        setSelectedCompany(jobDetails.client);
      }
    }
  }, [candidateToEdit, jobDetails]);

  // Prefill from API response if the candidate matches id
  useEffect(() => {
    if (candidateToEdit && apiResponse?.candidates?.length) {
      const apiCandidate = apiResponse.candidates.find((c: any) => c.id === candidateToEdit.id);
      if (apiCandidate) {
        setFormData(prev => ({
          ...prev,
          name: apiCandidate.name || prev.name,
          email: apiCandidate.email || prev.email,
          mobile: apiCandidate.mobile || prev.mobile,
          client: apiCandidate.client || prev.client,
          profile: apiCandidate.profile || prev.profile,
          skills: apiCandidate.skills || prev.skills,
          qualifications: apiCandidate.qualifications || prev.qualifications,
          currentCompany: apiCandidate.current_company || prev.currentCompany,
          currentJobLocation: apiCandidate.current_job_location || prev.currentJobLocation,
          preferredJobLocation: apiCandidate.preferred_job_location || prev.preferredJobLocation,
          reasonForJobChange: apiCandidate.reason_for_job_change || prev.reasonForJobChange,
          totalExperienceYears: apiCandidate.experience ? Math.floor(parseFloat(apiCandidate.experience)).toString() : prev.totalExperienceYears,
          totalExperienceMonths: apiCandidate.experience ? Math.round((parseFloat(apiCandidate.experience) % 1) * 12).toString() : prev.totalExperienceMonths,
          relevantExperienceYears: apiCandidate.relevant_experience ? Math.floor(parseFloat(apiCandidate.relevant_experience)).toString() : prev.relevantExperienceYears,
          relevantExperienceMonths: apiCandidate.relevant_experience ? Math.round((parseFloat(apiCandidate.relevant_experience) % 1) * 12).toString() : prev.relevantExperienceMonths,
          noticePeriod: apiCandidate.serving_notice_period || prev.noticePeriod,
          joiningOffer: apiCandidate.holding_offer || prev.joiningOffer,
          linkedinUrl: apiCandidate.linkedin || prev.linkedinUrl,
          remarks: apiCandidate.remarks || prev.remarks,
          // Parse currency and amounts like "USD 110000"
          currentCTC: (apiCandidate.current_ctc || '').replace(/^[A-Z]{3}\s*/i, ''),
          expectedCTC: (apiCandidate.expected_ctc || '').replace(/^[A-Z]{3}\s*/i, ''),
          currentCTCCurrencyType: (apiCandidate.current_ctc?.slice(0, 3)?.toUpperCase() as any) || prev.currentCTCCurrencyType,
          expectedCTCCurrencyType: (apiCandidate.expected_ctc?.slice(0, 3)?.toUpperCase() as any) || prev.expectedCTCCurrencyType,
        }));
      }
    }
  }, [candidateToEdit, apiResponse]);

  // Filter companies based on search term
  const filteredCompanies = clientCompanies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Paginate filtered companies
  const totalPages = Math.ceil(filteredCompanies.length / companiesPerPage);
  const startIndex = (currentPage - 1) * companiesPerPage;
  const paginatedCompanies = filteredCompanies.slice(startIndex, startIndex + companiesPerPage);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Get job IDs for the selected company
  const availableJobs = selectedCompany
    ? jobs.filter(job => job.client === selectedCompany && job.job_status === "Active")
    : [];

  // Handle company selection
  const handleCompanySelect = (companyId: string) => {
    setSelectedCompany(companyId);
    const company = clientCompanies.find((c) => c.id === companyId);
    if (company) {
      setFormData({
        ...formData,
        client: company.name,
        jobId: "", // Reset job ID when company changes
      });
    }
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData({
        ...formData,
        resume: e.target.files[0],
      });
    }
  };

  // Handle additional files upload
  const handleAdditionalFilesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData({
        ...formData,
        additionalFiles: e.target.files[0],
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    alert("Candidate registered successfully!");
    setSelectedCompany(null);
    setFormData({
      jobId: "",
      name: "",
      mobile: "",
      email: "",
      client: "",
      profile: "",
      skills: "",
      qualifications: "",
      resume: null,
      additionalFiles: null,
      // Compensation reset
      currentCTC: "",
      currentCTCCurrencyType: "INR",
      expectedCTC: "",
      expectedCTCCurrencyType: "INR",
      // Other details
      reasonForJobChange: "",
      currentCompany: "",
      currentJobPosition: "",
      currentJobLocation: "",
      preferredJobLocation: "",
      totalExperienceYears: "0",
      totalExperienceMonths: "0",
      relevantExperienceYears: "0",
      relevantExperienceMonths: "0",
      noticePeriod: "",
      joiningOffer: "",
      linkedinUrl: "",
      remarks: "",
    });
  };

  // Generate options for years (0-30)
  const yearOptions = Array.from({ length: 31 }, (_, i) => (
    <option key={`year-${i}`} value={i.toString()}>
      {i} {i === 1 ? "Year" : "Years"}
    </option>
  ));

  // Generate options for months (0-11)
  const monthOptions = Array.from({ length: 12 }, (_, i) => (
    <option key={`month-${i}`} value={i.toString()}>
      {i} {i === 1 ? "Month" : "Months"}
    </option>
  ));

  // Options for notice period and joining offer
  const noticePeriodOptions = [
    "None",
    "Immediate",
    "15 Days",
    "30 Days",
    "45 Days",
    "60 Days",
    "90 Days",
  ];

  const joiningOfferOptions = [
    "None",
    "Immediate",
    "15 Days",
    "30 Days",
    "45 Days",
    "60 Days",
  ];

  return (
    <div className="flex flex-col w-full h-full">
      <div className="bg-white p-6 flex-1 w-full overflow-auto flex flex-col">
        <div className="flex-1 overflow-auto">
          {!editMode && (
            <h2 className="text-2xl font-semibold text-gray-800 mb-2">{editMode ? "Edit Candidate" : "Register Candidate"}</h2>
          )}

          {!selectedCompany ? (
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 w-full">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-md font-medium text-gray-700">
                  Select Client Company
                </h3>
                <div className="text-sm text-gray-500">
                  {filteredCompanies.length} companies found
                </div>
              </div>

              {/* Search Bar */}
              <div className="mb-6">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search companies..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>

              {jobsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Loading companies...</div>
                </div>
              ) : filteredCompanies.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">
                    {searchTerm ? `No companies found matching "${searchTerm}"` : "No companies found"}
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6 min-h-[300px]">
                  {paginatedCompanies.map((company) => {
                    const styling = getCompanyStatusStyling(company.status);
                    return (
                      <div
                        key={company.id}
                        className={`border ${styling.border} ${styling.background} rounded-lg p-4 flex flex-col items-center ${styling.hover} cursor-pointer transition-colors relative`}
                        onClick={() => handleCompanySelect(company.id)}
                      >
                        {/* Status Badge */}
                        <div className={`absolute top-2 right-2 px-2 py-1 text-xs font-medium rounded-full ${styling.badge}`}>
                          {company.status}
                        </div>

                        {/* Company Avatar */}
                        <div className={`w-16 h-16 ${styling.background} border ${styling.border} rounded-full flex items-center justify-center mb-3 mt-2`}>
                          <span className={`text-xl font-bold ${styling.text}`}>
                            {company.name.charAt(0)}
                          </span>
                        </div>

                        {/* Company Name */}
                        <span className={`text-sm font-medium ${styling.text} text-center`}>
                          {company.name}
                        </span>

                        {/* Job Count Info */}
                        <div className="text-xs text-gray-500 mt-1 text-center">
                          {company.jobCounts.total} job{company.jobCounts.total !== 1 ? 's' : ''}
                          {company.jobCounts.active > 0 && (
                            <span className="text-green-600"> • {company.jobCounts.active} active</span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-700">
                    {filteredCompanies.length > 0 ? (
                      <>
                        Showing {startIndex + 1} to{" "}
                        {Math.min(startIndex + companiesPerPage, filteredCompanies.length)} of{" "}
                        {filteredCompanies.length} companies
                      </>
                    ) : (
                      "No companies found"
                    )}
                  </div>
                  <AnimatedPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={async (page) => {
                      await animatePagination();
                      setCurrentPage(page);
                    }}
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 w-full">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium text-gray-700">
                  {clientCompanies.find((c) => c.id === selectedCompany)?.name || formData.client}
                </h3>

                {!editMode && !jobDetails?.client && (
                  <button
                    onClick={() => setSelectedCompany(null)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Change Company
                  </button>
                )}
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  {/* Job ID */}
                  <div className="space-y-1">
                    <label
                      htmlFor="jobId"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Job ID
                    </label>
                    <select
                      id="jobId"
                      name="jobId"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={formData.jobId}
                      onChange={handleInputChange}
                    >
                      <option value="">Select Job ID</option>
                      {availableJobs.map((job) => (
                        <option key={job.id} value={job.id}>
                          {job.id} - {job.role}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Name */}
                  <div className="space-y-1">
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.name}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Mobile */}
                  <div className="space-y-1">
                    <label
                      htmlFor="mobile"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Mobile
                    </label>
                    <input
                      type="tel"
                      id="mobile"
                      name="mobile"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.mobile}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Email */}
                  <div className="space-y-1">
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Client */}
                  <div className="space-y-1">
                    <label
                      htmlFor="client"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Client
                    </label>
                    <input
                      type="text"
                      id="client"
                      name="client"
                      required
                      readOnly
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 sm:text-sm"
                      value={formData.client}
                    />
                  </div>

                  {/* Profile */}
                  <div className="space-y-1">
                    <label
                      htmlFor="profile"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Profile
                    </label>
                    <input
                      type="text"
                      id="profile"
                      name="profile"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.profile}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Skills */}
                  <div className="space-y-1">
                    <label
                      htmlFor="skills"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Skills
                    </label>
                    <input
                      type="text"
                      id="skills"
                      name="skills"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.skills}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Qualifications */}
                  <div className="space-y-1">
                    <label
                      htmlFor="qualifications"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Qualifications
                    </label>
                    <input
                      type="text"
                      id="qualifications"
                      name="qualifications"
                      required
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.qualifications}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current CTC */}
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700">Current CTC</label>
                    <div className="grid grid-cols-3 gap-2">
                      <select
                        id="currentCTCCurrencyType"
                        name="currentCTCCurrencyType"
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={formData.currentCTCCurrencyType}
                        onChange={handleInputChange}
                      >
                        <option value="INR">INR</option>
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                      </select>
                      <input
                        type="number"
                        id="currentCTC"
                        name="currentCTC"
                        placeholder="Amount"
                        className="col-span-2 mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        value={formData.currentCTC}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  {/* Expected CTC */}
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700">Expected CTC</label>
                    <div className="grid grid-cols-3 gap-2">
                      <select
                        id="expectedCTCCurrencyType"
                        name="expectedCTCCurrencyType"
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={formData.expectedCTCCurrencyType}
                        onChange={handleInputChange}
                      >
                        <option value="INR">INR</option>
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                      </select>
                      <input
                        type="number"
                        id="expectedCTC"
                        name="expectedCTC"
                        placeholder="Amount"
                        className="col-span-2 mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        value={formData.expectedCTC}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>

                  {/* Resume */}
                  <div className="space-y-1 ">
                    <label
                      htmlFor="resume"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Resume
                    </label>
                    <div className="mt-1 flex items-center">
                      <label
                        htmlFor="resume-upload"
                        className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Upload a file
                        <input
                          id="resume-upload"
                          name="resume-upload"
                          type="file"
                          className="sr-only"
                          accept=".pdf,.doc,.docx"
                          onChange={handleFileChange}
                        />
                      </label>
                      <span className="ml-3 text-sm text-gray-500">
                        {formData.resume
                          ? formData.resume.name
                          : "No file chosen"}
                      </span>
                      {formData.resume && (
                        <button
                          type="button"
                          className="ml-2 text-red-600 hover:text-red-800"
                          onClick={() =>
                            setFormData({ ...formData, resume: null })
                          }
                        >
                          Clear
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Additional Files */}
                  <div className="space-y-1 ">
                    <label
                      htmlFor="additionalFiles"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Additional Files
                    </label>
                    <div className="mt-1 flex items-center">
                      <label
                        htmlFor="additional-files-upload"
                        className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Upload a file
                        <input
                          id="additional-files-upload"
                          name="additional-files-upload"
                          type="file"
                          className="sr-only"
                          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                          onChange={handleAdditionalFilesChange}
                        />
                      </label>
                      <span className="ml-3 text-sm text-gray-500">
                        {formData.additionalFiles
                          ? formData.additionalFiles.name
                          : "No file chosen"}
                      </span>
                      {formData.additionalFiles && (
                        <button
                          type="button"
                          className="ml-2 text-red-600 hover:text-red-800"
                          onClick={() =>
                            setFormData({ ...formData, additionalFiles: null })
                          }
                        >
                          Clear
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Reason for Job Change */}
                  <div className="space-y-1 ">
                    <label
                      htmlFor="reasonForJobChange"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Reason for Job Change
                    </label>
                    <textarea
                      id="reasonForJobChange"
                      name="reasonForJobChange"
                      rows={2}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.reasonForJobChange}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current Company */}
                  <div className="space-y-1">
                    <label
                      htmlFor="currentCompany"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Current Company
                    </label>
                    <input
                      type="text"
                      id="currentCompany"
                      name="currentCompany"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.currentCompany}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current Job Position */}
                  <div className="space-y-1">
                    <label
                      htmlFor="currentJobPosition"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Current Job Position
                    </label>
                    <input
                      type="text"
                      id="currentJobPosition"
                      name="currentJobPosition"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.currentJobPosition}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Current Job Location */}
                  <div className="space-y-1">
                    <label
                      htmlFor="currentJobLocation"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Current Job Location
                    </label>
                    <input
                      type="text"
                      id="currentJobLocation"
                      name="currentJobLocation"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.currentJobLocation}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Preferred Job Location */}
                  <div className="space-y-1">
                    <label
                      htmlFor="preferredJobLocation"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Preferred Job Location
                    </label>
                    <input
                      type="text"
                      id="preferredJobLocation"
                      name="preferredJobLocation"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.preferredJobLocation}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Total Experience */}
                  <div className="space-y-1">
                    <label
                      htmlFor="totalExperience"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Total Experience
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <select
                        id="totalExperienceYears"
                        name="totalExperienceYears"
                        required
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={formData.totalExperienceYears}
                        onChange={handleInputChange}
                      >
                        <option value="">Years</option>
                        {yearOptions}
                      </select>
                      <select
                        id="totalExperienceMonths"
                        name="totalExperienceMonths"
                        required
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={formData.totalExperienceMonths}
                        onChange={handleInputChange}
                      >
                        <option value="">Months</option>
                        {monthOptions}
                      </select>
                    </div>
                  </div>

                  {/* Relevant Experience */}
                  <div className="space-y-1">
                    <label
                      htmlFor="relevantExperience"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Relevant Experience
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <select
                        id="relevantExperienceYears"
                        name="relevantExperienceYears"
                        required
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={formData.relevantExperienceYears}
                        onChange={handleInputChange}
                      >
                        <option value="">Years</option>
                        {yearOptions}
                      </select>
                      <select
                        id="relevantExperienceMonths"
                        name="relevantExperienceMonths"
                        required
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                        value={formData.relevantExperienceMonths}
                        onChange={handleInputChange}
                      >
                        <option value="">Months</option>
                        {monthOptions}
                      </select>
                    </div>
                  </div>

                  {/* Serving Notice Period */}
                  <div className="space-y-1">
                    <label
                      htmlFor="noticePeriod"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Serving Notice Period
                    </label>
                    <select
                      id="noticePeriod"
                      name="noticePeriod"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={formData.noticePeriod}
                      onChange={handleInputChange}
                    >
                      <option value="">Select</option>
                      {noticePeriodOptions.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Holding Offer */}
                  <div className="space-y-1">
                    <label
                      htmlFor="joiningOffer"
                      className="block text-sm font-medium text-gray-700"
                    >
                      * Holding Offer
                    </label>
                    <select
                      id="joiningOffer"
                      name="joiningOffer"
                      required
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                      value={formData.joiningOffer}
                      onChange={handleInputChange}
                    >
                      <option value="">Select</option>
                      {joiningOfferOptions.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* LinkedIn URL */}
                  <div className="space-y-1">
                    <label
                      htmlFor="linkedinUrl"
                      className="block text-sm font-medium text-gray-700"
                    >
                      LinkedIn URL
                    </label>
                    <input
                      type="url"
                      id="linkedinUrl"
                      name="linkedinUrl"
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.linkedinUrl}
                      onChange={handleInputChange}
                    />
                  </div>

                  {/* Remarks */}
                  <div className="space-y-1">
                    <label
                      htmlFor="remarks"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Remarks
                    </label>
                    <textarea
                      id="remarks"
                      name="remarks"
                      rows={2}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.remarks}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>


              </div>
            </div>
          )}
        </div>

        {/* Submit Button - Sticky to bottom, only show when company is selected */}
        {selectedCompany && (
          <div className="w-full p-4 flex justify-end bg-white shadow-top sticky bottom-0 border-t border-gray-200">
            <button
              type="button"
              onClick={handleSubmit}
              className="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              {editMode ? "Update Candidate" : "Register Candidate"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
