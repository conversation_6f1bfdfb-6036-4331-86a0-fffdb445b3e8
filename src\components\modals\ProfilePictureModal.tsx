import React, { useState, useRef, useCallback, useEffect } from "react";
import ReactCrop, { Crop, PixelCrop, centerCrop, makeAspectCrop } from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import { Upload, Camera, Trash2, AlertCircle } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface ProfilePictureModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageData: string) => Promise<void>;
  currentImage?: string;
  userName?: string;
}

const ACCEPTED_FILE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export function ProfilePictureModal({
  isOpen,
  onClose,
  onSave,
  currentImage,
  userName = "User",
}: ProfilePictureModalProps) {
  const [, setSelectedFile] = useState<File | null>(null);
  const [imageSrc, setImageSrc] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>("");
  const [isDragOver, setIsDragOver] = useState(false);
  const [aspectRatio, setAspectRatio] = useState<number | undefined>(1); // Square by default

  const imgRef = useRef<HTMLImageElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedFile(null);
      setImageSrc("");
      setCrop(undefined);
      setCompletedCrop(undefined);
      setError("");
      setIsDragOver(false);
    }
  }, [isOpen]);

  // Validate file type and size
  const validateFile = (file: File): string | null => {
    if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
      return "Please select a valid image file (JPEG, PNG, GIF, or WebP).";
    }
    if (file.size > MAX_FILE_SIZE) {
      return `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB.`;
    }
    if (file.size === 0) {
      return "The selected file appears to be empty or corrupted.";
    }
    return null;
  };

  // Validate image dimensions after loading
  const validateImageDimensions = (img: HTMLImageElement): string | null => {
    const minDimension = 50;
    const maxDimension = 4096;

    if (img.naturalWidth < minDimension || img.naturalHeight < minDimension) {
      return `Image is too small. Minimum dimensions: ${minDimension}x${minDimension}px.`;
    }
    if (img.naturalWidth > maxDimension || img.naturalHeight > maxDimension) {
      return `Image is too large. Maximum dimensions: ${maxDimension}x${maxDimension}px.`;
    }
    return null;
  };

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError("");
    setSelectedFile(file);

    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;

      // Create a temporary image to validate dimensions
      const tempImg = new Image();
      tempImg.onload = () => {
        const dimensionError = validateImageDimensions(tempImg);
        if (dimensionError) {
          setError(dimensionError);
          setSelectedFile(null);
          return;
        }
        setImageSrc(result);
      };
      tempImg.onerror = () => {
        setError("Failed to load image. The file may be corrupted or not a valid image.");
        setSelectedFile(null);
      };
      tempImg.src = result;
    };
    reader.onerror = () => {
      setError("Failed to read the selected file. Please try again.");
      setSelectedFile(null);
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // Handle image load for cropping
  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: "%",
          width: 80,
        },
        aspectRatio || 1,
        width,
        height
      ),
      width,
      height
    );
    
    setCrop(crop);
  };

  // Generate cropped image canvas
  const getCroppedImg = useCallback(
    (image: HTMLImageElement, crop: PixelCrop): Promise<string> => {
      const canvas = canvasRef.current;
      if (!canvas) {
        throw new Error("Canvas not found");
      }

      const ctx = canvas.getContext("2d");
      if (!ctx) {
        throw new Error("Canvas context not found");
      }

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;

      canvas.width = crop.width;
      canvas.height = crop.height;

      ctx.drawImage(
        image,
        crop.x * scaleX,
        crop.y * scaleY,
        crop.width * scaleX,
        crop.height * scaleY,
        0,
        0,
        crop.width,
        crop.height
      );

      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            throw new Error("Failed to create blob");
          }
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(blob);
        }, "image/jpeg", 0.9);
      });
    },
    []
  );

  // Handle save
  const handleSave = async () => {
    if (!imgRef.current || !completedCrop) {
      setError("Please select and crop an image first.");
      return;
    }

    // Validate crop dimensions
    if (completedCrop.width < 50 || completedCrop.height < 50) {
      setError("Cropped area is too small. Please select a larger area (minimum 50x50 pixels).");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const croppedImageData = await getCroppedImg(imgRef.current, completedCrop);

      // Validate the generated image data
      if (!croppedImageData || !croppedImageData.startsWith('data:image/')) {
        throw new Error("Failed to process the cropped image.");
      }

      await onSave(croppedImageData);
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to save image. Please try again.";
      setError(errorMessage);
      console.error("Error saving profile picture:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle remove picture
  const handleRemovePicture = async () => {
    if (!currentImage) {
      setError("No profile picture to remove.");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      await onSave(""); // Send empty string to remove picture
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to remove image. Please try again.";
      setError(errorMessage);
      console.error("Error removing profile picture:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Camera className="h-5 w-5" />
              Change Profile Picture
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Error Display */}
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span className="text-sm">{error}</span>
              </div>
            )}

            {/* File Upload Area */}
            {!imageSrc && (
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  isDragOver
                    ? "border-blue-400 bg-blue-50"
                    : "border-gray-300 hover:border-gray-400"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Upload a profile picture
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Drag and drop an image here, or click to select
                </p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  className="mb-2"
                >
                  Choose Image
                </Button>
                <p className="text-xs text-gray-400">
                  Supports: JPEG, PNG, GIF, WebP (max 5MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={ACCEPTED_FILE_TYPES.join(",")}
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>
            )}

            {/* Image Cropping Interface */}
            {imageSrc && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Crop your image</h3>
                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium">Aspect Ratio:</label>
                    <select
                      value={aspectRatio || "free"}
                      onChange={(e) => {
                        const value = e.target.value;
                        setAspectRatio(value === "free" ? undefined : parseFloat(value));
                      }}
                      className="px-2 py-1 border border-gray-300 rounded text-sm"
                    >
                      <option value="1">Square (1:1)</option>
                      <option value="1.33">Landscape (4:3)</option>
                      <option value="0.75">Portrait (3:4)</option>
                      <option value="free">Free</option>
                    </select>
                  </div>
                </div>

                <div className="border rounded-lg overflow-hidden bg-gray-50">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={aspectRatio}
                    minWidth={50}
                    minHeight={50}
                  >
                    <img
                      ref={imgRef}
                      src={imageSrc}
                      alt="Crop preview"
                      onLoad={onImageLoad}
                      className="max-w-full max-h-96 object-contain"
                    />
                  </ReactCrop>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={() => fileInputRef.current?.click()}
                    variant="outline"
                    size="sm"
                  >
                    Choose Different Image
                  </Button>
                  <Button
                    onClick={() => {
                      setImageSrc("");
                      setSelectedFile(null);
                    }}
                    variant="ghost"
                    size="sm"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {/* Current Image Display */}
            {currentImage && !imageSrc && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Current Profile Picture</h3>
                <div className="flex items-center gap-4">
                  <div className="h-24 w-24 rounded-full overflow-hidden bg-gray-100">
                    <img
                      src={currentImage}
                      alt={userName}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">
                      Upload a new image to replace your current profile picture.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="flex gap-2">
            {currentImage && (
              <Button
                onClick={handleRemovePicture}
                variant="destructive"
                disabled={isLoading}
                className="mr-auto"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Picture
              </Button>
            )}
            <Button onClick={onClose} variant="outline" disabled={isLoading}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!completedCrop || isLoading}
              className="min-w-[100px]"
            >
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Hidden canvas for image processing */}
      <canvas ref={canvasRef} className="hidden" />
    </>
  );
}
