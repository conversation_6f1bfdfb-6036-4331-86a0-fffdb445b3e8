import CryptoJS from 'crypto-js';
import { config } from '@/config/env';

const API_BASE_URL = config.apiBaseUrl;
const SECRET_KEY = 'ATS@mako';

// Types for authentication
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  status: string;
  message?: string;
  email?: string;
  name?: string;
  user_id?: number;
  redirect?: string;
  token?: string;
  peer_status?: boolean;
}

export interface ApiLoginRequest {
  username: string;
  password: string;
}

// Authentication service class
export class AuthService {
  // Encrypt password using AES encryption
  private static encryptPassword(password: string): string {
    return CryptoJS.AES.encrypt(password, SECRET_KEY).toString();
  }

  // Make login API request
  private static async makeLoginRequest(
    endpoint: string,
    credentials: LoginCredentials
  ): Promise<LoginResponse> {
    try {
      const encryptedPassword = this.encryptPassword(credentials.password);
      
      const requestBody: ApiLoginRequest = {
        username: credentials.username,
        password: encryptedPassword,
      };


      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      return result;
    } catch (error) {
      console.error('Login API request failed:', error);
      throw error;
    }
  }

  // Login for manager
  static async loginManager(credentials: LoginCredentials): Promise<LoginResponse> {
    return this.makeLoginRequest('/login/management', credentials);
  }

  // Login for recruiter
  static async loginRecruiter(credentials: LoginCredentials): Promise<LoginResponse> {
    return this.makeLoginRequest('/login/recruiter', credentials);
  }

  // Generic login method that determines endpoint based on role
  static async login(
    credentials: LoginCredentials,
    role: 'manager' | 'recruiter'
  ): Promise<LoginResponse> {
    if (role === 'manager') {
      return this.loginManager(credentials);
    } else {
      return this.loginRecruiter(credentials);
    }
  }
}
