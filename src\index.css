@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Custom animations for better UX */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.5rem;

  /* TalentTrack Pro Corporate Blue Theme */
  --background: #ffffff;
  --foreground: #1e293b;
  --card: #ffffff;
  --card-foreground: #1e293b;
  --popover: #ffffff;
  --popover-foreground: #1e293b;

  /* Primary Blue Palette */
  --primary: #1e40af;
  --primary-foreground: #ffffff;
  --primary-hover: #1d4ed8;
  --primary-light: #3b82f6;
  --primary-dark: #1e3a8a;

  /* Secondary Colors */
  --secondary: #eff6ff;
  --secondary-foreground: #1e40af;
  --secondary-hover: #dbeafe;

  /* Muted Colors */
  --muted: #f8fafc;
  --muted-foreground: #64748b;

  /* Accent Colors */
  --accent: #eff6ff;
  --accent-foreground: #1e40af;

  /* Status Colors */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --info: #06b6d4;
  --info-foreground: #ffffff;

  /* Border & Input */
  --border: #e2e8f0;
  --input: #e2e8f0;
  --input-focus: #1e40af;
  --ring: #3b82f6;

  /* Chart Colors */
  --chart-1: #1e40af;
  --chart-2: #10b981;
  --chart-3: #f59e0b;
  --chart-4: #ef4444;
  --chart-5: #06b6d4;

  /* Sidebar Corporate Blue */
  --sidebar: #02026e;
  --sidebar-foreground: #dbeafe;
  --sidebar-primary: #1e40ff;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #1e40af;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #1e40af;
  --sidebar-ring: #3b82f6;
  --sidebar-hover: #0b0bbf;
  --sidebar-text-secondary: #93c5fd;
}

.dark {
  --background: #090b17;
  --foreground: #fdfdff;
  --card: #131e33;
  --card-foreground: #fdfdff;
  --popover: #131e33;
  --popover-foreground: #fdfdff;

  --primary: #e1e7ff;
  --primary-foreground: #131e33;

  --secondary: #2a3859;
  --secondary-foreground: #fdfdff;

  --muted: #2a3859;
  --muted-foreground: #a6b6e7;

  --accent: #2a3859;
  --accent-foreground: #fdfdff;

  --destructive: #e84d3c;
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.15);
  --ring: #5361a8;

  --chart-1: #374c9b;
  --chart-2: #60cb89;
  --chart-3: #f1bb50;
  --chart-4: #a355b3;
  --chart-5: #dc6a45;

  --sidebar: #131e33;
  --sidebar-foreground: #fdfdff;
  --sidebar-primary: #374c9b;
  --sidebar-primary-foreground: #fdfdff;
  --sidebar-accent: #2a3859;
  --sidebar-accent-foreground: #fdfdff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: #5361a8;
}

@layer base {
  * {
    border-color: var(--border);
    outline-color: rgb(148 163 184 / 0.5);
  }

  html,
  body {
    height: 100%;
  }

  body {
    /* Use theme background instead of hardcoded blue (#eff6ff) */
    background-color: var(--background);
    /* Prevent outer body scroll; let app layout manage scrolling */
    overflow: hidden;
    color: var(--foreground);
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
      sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: var(--primary-dark);
    font-weight: 600;
  }
}

/* Custom utility classes */
@layer utilities {
  .shadow-top {
    box-shadow: 0 -1px 3px 0 rgb(0 0 0 / 0.1), 0 -1px 2px -1px rgb(0 0 0 / 0.1);
  }
}
