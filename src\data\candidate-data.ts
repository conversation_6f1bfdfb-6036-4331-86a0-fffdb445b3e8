import { Candidate } from "@/types/candidate";

// Generate sample candidate data
const generateSampleData = (count: number): Candidate[] => {
  return Array.from({ length: count }, (_, i) => {
    return {
      id: i + 1,
      jobId: `JOB-${2023 + Math.floor(Math.random() * 100)}`,
      firstName: `First${i + 1}`,
      lastName: `Last${i + 1}`,
      email: `candidate${i + 1}@example.com`,
      phone: `+1 (555) ${100 + i}-${1000 + i}`,
      client: [
        "Acme Corp",
        "TechGiant",
        "Innovate Inc",
        "Global Systems",
        "NextGen",
      ][Math.floor(Math.random() * 5)],
      profile: [
        "Frontend Developer",
        "Backend Engineer",
        "Full Stack",
        "DevOps",
        "UI/UX Designer",
      ][Math.floor(Math.random() * 5)],
      status: ["New", "Screening", "Interview", "Offer", "Rejected", "Hired"][
        Math.floor(Math.random() * 6)
      ] as "New" | "Screening" | "Interview" | "Offer" | "Rejected" | "Hired",
      appliedDate: new Date(
        2023 + Math.floor(Math.random() * 2),
        Math.floor(Math.random() * 12),
        Math.floor(Math.random() * 28) + 1
      )
        .toISOString()
        .split("T")[0],
      source: ["LinkedIn", "Indeed", "Referral", "Company Website", "Job Fair"][
        Math.floor(Math.random() * 5)
      ],
      experience: Math.floor(Math.random() * 15),
      education: ["Bachelor's", "Master's", "PhD", "High School", "Associate's"][
        Math.floor(Math.random() * 5)
      ],
      skills: [
        "JavaScript, React, HTML",
        "Python, Django, SQL",
        "Java, Spring, AWS",
        "C#, .NET, Azure",
        "PHP, Laravel, MySQL",
      ][Math.floor(Math.random() * 5)],
      location: ["New York", "Remote", "San Francisco", "Chicago", "Austin"][
        Math.floor(Math.random() * 5)
      ],
      salary: `$${70 + Math.floor(Math.random() * 80)}k - $${
        100 + Math.floor(Math.random() * 50)
      }k`,
      notes: [
        "Strong candidate",
        "Needs technical assessment",
        "Good cultural fit",
        "Pending reference check",
        "Scheduling follow-up",
      ][Math.floor(Math.random() * 5)],
      lastUpdated: new Date(
        2024,
        Math.floor(Math.random() * 12),
        Math.floor(Math.random() * 28) + 1,
        Math.floor(Math.random() * 24),
        Math.floor(Math.random() * 60)
      )
        .toISOString()
        .split("T")[0],
      comment: [
        "Excellent technical skills",
        "Good communication",
        "Needs more experience",
        "Perfect fit for the role",
        "Requires salary negotiation",
        "Available immediately",
        "Notice period: 30 days",
      ][Math.floor(Math.random() * 7)],
      peerReviewer: [
        "Sarah Johnson",
        "Mike Chen",
        "Emily Davis",
        "Alex Rodriguez",
        "Jessica Kim",
        "David Wilson",
        "Lisa Thompson",
      ][Math.floor(Math.random() * 7)],
      recruiter: [
        "John Smith",
        "Maria Garcia",
        "Robert Brown",
        "Jennifer Lee",
        "Michael Taylor",
        "Amanda White",
        "Christopher Jones",
      ][Math.floor(Math.random() * 7)],
    };
  });
};

// Export the candidate data (50 sample candidates)
export const candidatesData: Candidate[] = generateSampleData(50);
