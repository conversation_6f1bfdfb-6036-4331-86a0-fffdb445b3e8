import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Line,
  <PERSON>atter<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
} from "recharts";
import {
  File,
  Send,
  Briefcase,
  BrainCircuit,
  DollarSign,
  TrendingUp,
  Download,
  BarChart2,
  LineChart as LineChartIcon,
  Building,
  AlertTriangle,
  User,
  CheckCircle,
} from "lucide-react";
import { ApiService, type CandidateAnalysisResponse } from "@/services/api";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchJobs } from "@/store/slices/jobsSlice";
import { selectJobs } from "@/store/selectors/jobsSelectors";

// --- Default/Empty Data for Charts ---
const defaultExpertiseData = [{ name: "No Data", count: 0 }];

const defaultMarketRelevanceData = [{ x: "No Data", y: 0, z: 100 }];

const defaultLearningAttitudeData = [{ name: "No Data", "Skills Count": 0 }];

const defaultBudgetData = {
  candidateName: "No Candidate Selected",
  overallExperience: 0,
  jdExperience: "0 - 0",
  minPackage: 0,
  maxPackage: 0,
};

const defaultCareerData = [
  {
    title: "No Data Available",
    projects:
      "Please upload a resume and select a job to view career information",
    company: "N/A",
    location: "N/A",
    from: "N/A",
    to: "N/A",
    icon: <Building className="w-6 h-6 text-white" />,
    iconBg: "bg-gray-500",
  },
];

// --- API Function ---
const analyzeCandidate = async (
  userId: string,
  jobId: string,
  resumeBase64: string
): Promise<CandidateAnalysisResponse> => {
  return ApiService.analyzeCandidateProfile(userId, jobId, resumeBase64);
};

// --- Main ProfileAnalysis Component ---
export function ProfileAnalysis() {
  const [activeTab, setActiveTab] = useState("Budget Estimate");
  const [loading, setLoading] = useState(false);
  const [fileName, setFileName] = useState("");
  const [apiData, setApiData] = useState<CandidateAnalysisResponse | null>(
    null
  );
  const [selectedJobId, setSelectedJobId] = useState("");
  const [client, setClient] = useState("");

  // Redux state
  const dispatch = useAppDispatch();
  const allJobs = useAppSelector(selectJobs);

  // Get current recruiter name and filter jobs
  const currentRecruiterName = localStorage.getItem("name") || "";
  const assignedJobs = allJobs.filter((job) => {
    return job.recruiter.split(", ").includes(currentRecruiterName);
  });

  // Fetch jobs data on component mount
  useEffect(() => {
    if (currentRecruiterName) {
      dispatch(fetchJobs({ username: currentRecruiterName }));
    }
  }, [dispatch, currentRecruiterName]);

  // Update client when job is selected
  useEffect(() => {
    if (selectedJobId) {
      const selectedJob = assignedJobs.find(
        (job) => job.id.toString() === selectedJobId
      );
      if (selectedJob) {
        setClient(selectedJob.client);
      }
    } else {
      setClient("");
    }
  }, [selectedJobId, assignedJobs]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setFileName(event.target.files[0].name);
    } else {
      setFileName("");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!fileName) {
      alert("Please select a file first");
      return;
    }

    if (!selectedJobId) {
      alert("Please select a job first");
      return;
    }

    setLoading(true);

    try {
      const fileInput = document.getElementById(
        "resume-upload"
      ) as HTMLInputElement;
      if (fileInput?.files?.[0]) {
        const file = fileInput.files[0];
        const reader = new FileReader();

        reader.onload = async () => {
          try {
            const base64 = reader.result as string;
            const base64Data = base64.split(",")[1]; // Remove data:application/pdf;base64, prefix

            const response = await analyzeCandidate(
              "52",
              selectedJobId,
              base64Data
            );
            setApiData(response);
          } catch (error) {
            console.error("Error analyzing candidate:", error);
          } finally {
            setLoading(false);
          }
        };

        reader.readAsDataURL(file);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error("Error processing file:", error);
      setLoading(false);
    }
  };

  const renderContent = () => {
    if (loading) {
      return <LoadingState />;
    }

    switch (activeTab) {
      case "Expertise Level":
        return <ExpertiseLevelChart apiData={apiData} />;
      case "Market Relevance":
        return <MarketRelevanceChart apiData={apiData} />;
      case "Learning Attitude":
        return <LearningAttitudeChart apiData={apiData} />;
      case "Budget Estimate":
        return <BudgetEstimateChart apiData={apiData} />;
      case "Career Growth":
        return <CareerGrowthChart apiData={apiData} />;
      default:
        return <ExpertiseLevelChart apiData={apiData} />;
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen font-sans p-1 sm:p-2 lg:p-3">
      <div className="max-w-7xl mx-auto">
        <header className="bg-white p-2 rounded-lg shadow-sm mb-3">
          <form
            onSubmit={handleSubmit}
            className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-2 items-end"
          >
            <div className="lg:col-span-1">
              <label
                htmlFor="jobId"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Job Id
              </label>
              <select
                id="jobId"
                value={selectedJobId}
                onChange={(e) => setSelectedJobId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">Select a Job</option>
                {assignedJobs.map((job) => (
                  <option key={job.id} value={job.id.toString()}>
                    {job.id} - {job.role}
                  </option>
                ))}
              </select>
            </div>
            <div className="lg:col-span-1">
              <label
                htmlFor="client"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Client
              </label>
              <input
                type="text"
                id="client"
                value={client}
                readOnly
                placeholder="Select a job to see client"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-600"
              />
            </div>
            <div className="lg:col-span-1">
              <label
                htmlFor="resume"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Upload Resume
              </label>
              <div className="flex">
                <label
                  htmlFor="resume-upload"
                  className="flex-grow cursor-pointer bg-white border border-gray-300 rounded-l-md px-3 py-2 text-gray-700 hover:bg-gray-50"
                >
                  {fileName || "Choose File"}
                </label>
                <input
                  id="resume-upload"
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                />
                <span className="inline-flex items-center px-3 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm rounded-r-md">
                  <File className="h-5 w-5" />
                </span>
              </div>
            </div>
            <div className="lg:col-span-1">
              <button
                type="submit"
                className="w-full flex justify-center items-center gap-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    Submit
                  </>
                )}
              </button>
            </div>
          </form>
        </header>

        <nav className="flex flex-wrap gap-2 mb-2">
          <TabButton
            icon={<BarChart2 />}
            text="Expertise Level"
            activeTab={activeTab}
            onClick={setActiveTab}
          />
          <TabButton
            icon={<LineChartIcon />}
            text="Market Relevance"
            activeTab={activeTab}
            onClick={setActiveTab}
          />
          <TabButton
            icon={<BrainCircuit />}
            text="Learning Attitude"
            activeTab={activeTab}
            onClick={setActiveTab}
          />
          <TabButton
            icon={<DollarSign />}
            text="Budget Estimate"
            activeTab={activeTab}
            onClick={setActiveTab}
          />
          <TabButton
            icon={<TrendingUp />}
            text="Career Growth"
            activeTab={activeTab}
            onClick={setActiveTab}
          />
        </nav>

        <main className="bg-white p-4 sm:p-6 rounded-lg shadow-sm overflow-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
}

// --- Tab Button Component ---Expertise Level

const TabButton = ({
  icon,
  text,
  activeTab,
  onClick,
}: {
  icon: React.ReactElement<{ className?: string }>;
  text: string;
  activeTab: string;
  onClick: (tab: string) => void;
}) => {
  const isActive = activeTab === text;
  return (
    <button
      onClick={() => onClick(text)}
      className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md border transition-all duration-200 ${
        isActive
          ? "bg-white text-indigo-600 border-indigo-500 shadow-md"
          : "bg-white text-gray-600 border-gray-300 hover:bg-gray-100 hover:border-gray-400"
      }`}
    >
      {React.cloneElement(icon as React.ReactElement<{ className?: string }>, {
        className: "h-5 w-5",
      })}
      <span>{text}</span>
    </button>
  );
};

// --- Chart/Content Components ---
const ChartContainer = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => (
  <div>
    <div className="flex justify-between items-center mb-6">
      <h2 className="text-xl font-semibold text-indigo-700">{title}</h2>
      <button className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md text-sm hover:bg-slate-800 transition-colors">
        <Download className="h-4 w-4" />
        Export
      </button>
    </div>
    <div className="w-full">{children}</div>
  </div>
);

const ExpertiseLevelChart = ({
  apiData,
}: {
  apiData: CandidateAnalysisResponse | null;
}) => {
  const chartData =
    apiData?.expertise_response?.categories?.map((cat) => ({
      name: cat.Category,
      count: cat.Items.length,
    })) || defaultExpertiseData;

  return (
    <ChartContainer title="Expertise Level">
      <div style={{ width: "100%", height: 350 }}>
        <ResponsiveContainer>
          <BarChart
            data={chartData}
            margin={{ top: 5, right: 20, left: 10, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="name" tick={{ fontSize: 12 }} />
            <YAxis
              label={{
                value: "Expertise Levels",
                angle: -90,
                position: "insideLeft",
                fill: "#4F46E5",
                style: { fontSize: "14px" },
              }}
            />
            <Tooltip />
            <Legend
              verticalAlign="bottom"
              wrapperStyle={{ paddingTop: "20px" }}
            />
            <Bar dataKey="count" fill="#818cf8" barSize={50} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </ChartContainer>
  );
};

const MarketRelevanceChart = ({
  apiData,
}: {
  apiData: CandidateAnalysisResponse | null;
}) => {
  const chartData =
    apiData?.analyze_candidate_profile_response?.map((item) => ({
      x: item["Skill/Domain"],
      y: item["Relevance Score"],
      z:
        item.Experience === "High"
          ? 400
          : item.Experience === "Moderate"
          ? 300
          : 200,
    })) || defaultMarketRelevanceData;

  return (
    <ChartContainer title="Market Relevance">
      <div style={{ width: "100%", height: 350 }}>
        <ResponsiveContainer>
          <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              type="category"
              dataKey="x"
              name="Skills"
              tick={{ fontSize: 12 }}
              label={{
                value: "Skills",
                position: "insideBottom",
                offset: -15,
                fill: "#4F46E5",
                style: { fontSize: "14px" },
              }}
            />
            <YAxis
              type="number"
              dataKey="y"
              name="Relevance Scores"
              label={{
                value: "Relevance Scores",
                angle: -90,
                position: "insideLeft",
                fill: "#4F46E5",
                style: { fontSize: "14px" },
              }}
            />
            <ZAxis
              type="number"
              dataKey="z"
              range={[100, 1000]}
              name="volume"
              unit="px"
            />
            <Tooltip cursor={{ strokeDasharray: "3 3" }} />
            <Scatter name="Market Relevance" data={chartData} fill="#6366f1" />
          </ScatterChart>
        </ResponsiveContainer>
      </div>
    </ChartContainer>
  );
};

const LearningAttitudeChart = ({
  apiData,
}: {
  apiData: CandidateAnalysisResponse | null;
}) => {
  const chartData =
    apiData?.candidate_learning_response?.["Company Names"]?.map((company) => ({
      name: company,
      "Skills Count":
        apiData.candidate_learning_response["Technologies Used"][company]
          ?.length || 0,
    })) || defaultLearningAttitudeData;

  return (
    <ChartContainer title="Learning Attitude">
      <div style={{ width: "100%", height: 350 }}>
        <ResponsiveContainer>
          <LineChart
            data={chartData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis
              label={{
                value: "Skills Count",
                angle: -90,
                position: "insideLeft",
                fill: "#4F46E5",
                style: { fontSize: "14px" },
              }}
              domain={[0, "dataMax"]}
            />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="Skills Count"
              stroke="#8884d8"
              strokeWidth={2}
              activeDot={{ r: 8 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </ChartContainer>
  );
};

// --- New Budget Estimate Component ---
const BudgetEstimateChart = ({
  apiData,
}: {
  apiData: CandidateAnalysisResponse | null;
}) => {
  // --- Data Extraction (No changes here) ---
  const candidateName =
    apiData?.job_info_response?.Candidate?.[0] ||
    defaultBudgetData.candidateName;
  const candidateExperience =
    parseFloat(
      apiData?.job_info_response?.["Candidate Experience"]?.[0] || "0"
    ) || defaultBudgetData.overallExperience;
  const minPackage =
    parseFloat(
      apiData?.job_info_response?.["Job Description Min Package (LPA)"]?.[0] ||
        "0"
    ) || defaultBudgetData.minPackage;
  const maxPackage =
    parseFloat(
      apiData?.job_info_response?.["Job Description Max Package (LPA)"]?.[0] ||
        "0"
    ) || defaultBudgetData.maxPackage;
  const jdExperience = `${
    apiData?.job_info_response?.["Job Description Min Experience"]?.[0] || "0"
  } - ${
    apiData?.job_info_response?.["Job Description Max Experience"]?.[0] || "0"
  }`;
  const candidatePackageMin =
    parseFloat(
      apiData?.job_info_response?.["Candidate Minimum Budget"]?.[0] || "0"
    ) || defaultBudgetData.minPackage;
  const candidatePackageMax =
    parseFloat(
      apiData?.job_info_response?.["Candidate Maximum Budget"]?.[0] || "0"
    ) || defaultBudgetData.maxPackage;
  const jdMinPackage = minPackage;
  const jdMaxPackage = maxPackage;

  // --- UI Component ---
  return (
    <ChartContainer title="Budget Estimate">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800">{candidateName}</h3>
      </div>

      {/* Main two-column layout to fit all content on screen */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
        {/* Left Column: Analysis Summaries */}
        <div className="space-y-6">
          {/* Experience Analysis */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-700 mb-3">
              Experience Analysis
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Candidate Experience:</span>
                <span className="font-semibold text-gray-800">
                  {candidateExperience} years
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Required Experience:</span>
                <span className="font-semibold text-gray-800">
                  {jdExperience} years
                </span>
              </div>
            </div>
          </div>

          {/* Budget Match Analysis */}
          <div
            className={`p-4 rounded-lg ${
              candidatePackageMin >= jdMinPackage &&
              candidatePackageMax <= jdMaxPackage &&
              jdMinPackage != 0
                ? "bg-green-100 text-green-800"
                : candidatePackageMax < jdMinPackage ||
                  (candidatePackageMin > jdMaxPackage && jdMinPackage != 0)
                ? "bg-red-100 text-red-800"
                : "bg-yellow-100 text-yellow-800"
            }`}
          >
            <div className="flex items-center">
              {jdMinPackage === 0 ? (
                <>
                  <CheckCircle className="h-5 w-5 mr-3 shrink-0" />
                  <p className="font-medium">
                    No budget specified in job description JD OR No budget
                    specified in candidate profile.
                  </p>
                </>
              ) : null}

              {candidatePackageMin >= jdMinPackage &&
              candidatePackageMax <= jdMaxPackage &&
              jdMinPackage != 0 ? (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3 shrink-0"></div>
                  <p className="font-medium">
                    Perfect Match! Candidate expectations align with job budget.
                  </p>
                </>
              ) : candidatePackageMax < jdMinPackage ||
                (candidatePackageMin > jdMaxPackage && jdMinPackage != 0) ? (
                <>
                  <AlertTriangle className="h-5 w-5 mr-3 shrink-0" />
                  <p className="font-medium">
                    Budget Mismatch! No overlap in budget ranges.
                  </p>
                </>
              ) : jdMinPackage != 0 ? (
                <>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3 shrink-0"></div>
                  <p className="font-medium">
                    Partial Match! Some overlap between budget ranges.
                  </p>
                </>
              ) : null}
            </div>
          </div>
        </div>

        {/* Right Column: Combined Budget Comparison Chart */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-lg font-semibold text-gray-700 mb-4">
            Budget Comparison
          </h4>
          <div className="space-y-3">
            {/* Combined Slider visualization */}
            <div className="relative h-8 bg-gray-200 rounded-full">
              {/* Job Description Budget Bar */}
              <div
                className="absolute h-8 bg-gradient-to-r from-green-300 to-green-500 rounded-full"
                style={{
                  left: `calc(${(jdMinPackage / 20) * 100}%)`,
                  width: `calc(${((jdMaxPackage - jdMinPackage) / 20) * 100}%)`,
                }}
              ></div>
              {/* Candidate Budget Bar */}
              <div
                className="absolute h-8 bg-gradient-to-r from-blue-300 to-blue-500 rounded-full opacity-75 border-2 border-blue-600"
                style={{
                  left: `calc(${(candidatePackageMin / 20) * 100}%)`,
                  width: `calc(${
                    ((candidatePackageMax - candidatePackageMin) / 20) * 100
                  }%)`,
                }}
              ></div>
            </div>

            {/* Legend and Details */}
            <div className="grid grid-cols-2 gap-x-4 gap-y-2 pt-2">
              {/* Candidate Details */}
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-blue-600" />
                <span className="font-semibold text-blue-700">
                  Candidate: ₹{candidatePackageMin}L - ₹{candidatePackageMax}L
                </span>
              </div>
              {/* Job Details */}
              <div className="flex items-center space-x-2">
                <Briefcase className="w-4 h-4 text-green-600" />
                <span className="font-semibold text-green-700">
                  Job: ₹{jdMinPackage}L - ₹{jdMaxPackage}L
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ChartContainer>
  );
};

// --- Career Growth Components ---
const CareerGrowthChart = ({
  apiData,
}: {
  apiData: CandidateAnalysisResponse | null;
}) => {
  const careerDataToUse =
    apiData?.career_progress_response?.map((item) => ({
      title: item.Title,
      projects: item.Project,
      company: item.Company,
      location: item.Location,
      from: item["From Date"],
      to: item["To Date"],
      icon: <Building className="w-6 h-6 text-white" />,
      iconBg: "bg-blue-500",
    })) || defaultCareerData;

  return (
    <ChartContainer title="Career Growth">
      <div style={{ width: "100%", height: 350 }} className="relative pl-3">
        <div className="space-y-12">
          {careerDataToUse.map((item, index) => (
            <div key={index} className="relative">
              <div
                className={`absolute left-[-1.8rem] top-0 w-10 h-10 rounded-full ${item.iconBg} flex items-center justify-center shadow-lg`}
              >
                {item.icon}
              </div>
              <div className="ml-8 bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                <h3 className="font-bold text-lg text-indigo-600">
                  {item.title}
                </h3>
                <p className="mt-2 text-sm text-gray-700">
                  <span className="font-semibold">Projects:</span>{" "}
                  {item.projects}
                </p>
                <div className="mt-3 text-xs text-gray-500 grid grid-cols-2 gap-2">
                  <div>
                    <span className="font-semibold">Company:</span>{" "}
                    {item.company}
                  </div>
                  <div>
                    <span className="font-semibold">Location:</span>{" "}
                    {item.location}
                  </div>
                  <div>
                    <span className="font-semibold">From:</span> {item.from}
                  </div>
                  <div>
                    <span className="font-semibold">To:</span> {item.to}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </ChartContainer>
  );
};

const LoadingState = () => (
  <div className="w-full h-[450px] border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center">
    <div className="flex items-center justify-center space-x-2">
      <div className="w-4 h-4 rounded-full bg-indigo-600 animate-pulse [animation-delay:-0.3s]"></div>
      <div className="w-4 h-4 rounded-full bg-indigo-600 animate-pulse [animation-delay:-0.15s]"></div>
      <div className="w-4 h-4 rounded-full bg-indigo-600 animate-pulse"></div>
    </div>
    <p className="mt-4 text-gray-500">Analyzing Resume...</p>
  </div>
);
