import { useState } from "react";
import { ChevronDown, Home } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { SidebarTrigger } from "@/components/ui/sidebar";
import ATS_LOGO from "@/assets/ATS_LOGO.png";

interface HeaderProps {
  userName?: string;
  userEmail?: string;
  userAvatar?: string;
}

export function Header({
  userName = "John Doe",
  userEmail = "<EMAIL>",
  userAvatar,
}: HeaderProps) {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const navigate = useNavigate();

  const toggleProfileMenu = () => {
    setIsProfileMenuOpen(!isProfileMenuOpen);
  };

  const handleHomeClick = () => {
    navigate("/landing");
  };

  return (
    <header className="bg-white border-b border-gray-200 py-1 px-6 flex items-center  justify-between">
      {/* Sidebar Toggle */}
      <div className="flex items-center">
        <SidebarTrigger className="mr-4" />
      </div>
      <div className="flex items-center -ml-90">
        <img src={ATS_LOGO} alt="ATS Logo" className="h-12 w-auto " />
      </div>
      <div className="text-xl font-bold text-blue-900">
        {" "}
        Makonis TalentTrack Pro
      </div>

      <div className="flex items-center space-x-4">
        {/* Home Button */}
        <button
          onClick={handleHomeClick}
          className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
          title="Go to Landing Page"
        >
          <Home className="h-4 w-4" />
          <span className="hidden md:inline">Home</span>
        </button>

        {/* Profile */}
        <div className="relative">
          <button
            onClick={toggleProfileMenu}
            className="flex items-center space-x-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
          >
            <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white overflow-hidden">
              {userAvatar ? (
                <img
                  src={userAvatar}
                  alt={userName}
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="font-medium text-sm">
                  {userName.charAt(0)}
                </span>
              )}
            </div>
            <span className="hidden md:inline-block font-medium text-gray-700">
              {userName}
            </span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </button>

          {/* Profile Dropdown */}
          {isProfileMenuOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200">
                <p className="text-sm font-medium text-gray-900">{userName}</p>
                <p className="text-xs text-gray-500 truncate">{userEmail}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
