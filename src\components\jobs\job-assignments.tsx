import { useState, useEffect } from "react";
import { useUser } from "@/contexts/user-context";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchJobs } from "@/store/slices/jobsSlice";
import {
  selectJobs,
  selectJobsLoading,
  selectJobsError,
  selectUniqueClients,
  selectUniqueRoles,
  selectUniqueRecruiters,
} from "@/store/selectors/jobsSelectors";
import { SearchableDropdown } from "@/components/ui/searchable-dropdown";

// Job types as per requirements
const jobTypes = [
  "Permanent with Client",
  "Permanent with Makonis",
  "Contract",
  "Custom"
];

// Modes of work as per requirements
const modesOfWork = ["Hybrid", "WFO", "WFH"];

// Shift timings as per requirements
const shiftTimings = ["General", "Rotational"];

// Job statuses as per requirements
const jobStatuses = ["Active", "Close", "Hold"];

// Currency options for budget
const currencies = [
  { code: "INR", label: "INR (LPA)", symbol: "₹" },
  { code: "USD", label: "USD", symbol: "$" },
  { code: "EUR", label: "EUR", symbol: "€" },
  { code: "CAD", label: "CAD", symbol: "C$" }
];

// Common countries
const countries = [
  "India",
  "United States",
  "United Kingdom",
  "Canada",
  "Australia",
  "Germany",
  "France",
  "Singapore",
  "UAE",
  "Netherlands",
  "Switzerland",
  "Sweden",
  "Norway",
  "Denmark",
  "Other"
];



// Interface for form data
interface JobFormData {
  client: string;
  isNewClient: boolean;
  role: string;
  isNewRole: boolean;
  skills: string;
  country: string;
  place: string;
  modeOfWork: string;
  maxExperience: string;
  minExperience: string;
  maxBudget: string;
  minBudget: string;
  currency: string;
  noticePeriod: string;
  jobType: string;
  numberOfPositions: string;
  shiftTimings: string;
  jobStatus: string;
  recruiter: string;
  detailedJD: File | null;
  jobDescription: string;
}

export function JobAssignments() {
  const { userName, userEmail } = useUser();
  const dispatch = useAppDispatch();

  // Redux state
  const jobs = useAppSelector(selectJobs);
  const loading = useAppSelector(selectJobsLoading);
  const error = useAppSelector(selectJobsError);

  // Get all unique values for suggestions
  const allClients = useAppSelector(selectUniqueClients);
  const allRoles = useAppSelector(selectUniqueRoles);
  const allRecruiters = useAppSelector(selectUniqueRecruiters);

  // State for form data
  const [formData, setFormData] = useState<JobFormData>({
    client: "",
    isNewClient: false,
    role: "",
    isNewRole: false,
    skills: "",
    country: "",
    place: "",
    modeOfWork: "",
    maxExperience: "",
    minExperience: "",
    maxBudget: "",
    minBudget: "",
    currency: "INR",
    noticePeriod: "",
    jobType: "",
    numberOfPositions: "",
    shiftTimings: "",
    jobStatus: "",
    recruiter: "",
    detailedJD: null,
    jobDescription: "",
  });

  // Fetch jobs data on component mount
  useEffect(() => {
    if (jobs.length === 0) {
      dispatch(fetchJobs({ username: userName || userEmail || "managerone" }));
    }
  }, [dispatch, userName, userEmail, jobs.length]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData({
        ...formData,
        detailedJD: e.target.files[0],
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    alert("Job posted successfully!");
    setFormData({
      client: "",
      isNewClient: false,
      role: "",
      isNewRole: false,
      skills: "",
      country: "",
      place: "",
      modeOfWork: "",
      maxExperience: "",
      minExperience: "",
      maxBudget: "",
      minBudget: "",
      currency: "INR",
      noticePeriod: "",
      jobType: "",
      numberOfPositions: "",
      shiftTimings: "",
      jobStatus: "",
      recruiter: "",
      detailedJD: null,
      jobDescription: "",
    });
  };

  if (loading) {
    return (
      <div className="w-full max-h-[calc(100vh-200px)] bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading job data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full max-h-[calc(100vh-200px)] bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading job data: {error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full bg-gray-50">
      <div className="w-full h-full bg-white p-6 flex flex-col">
        <div className="flex-1 overflow-auto">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">
            Job Assignments
          </h2>
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 w-full">
            <form onSubmit={handleSubmit} className="w-full max-w-none">
              <div className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 gap-x-6 gap-y-4 w-full">
                <SearchableDropdown
                  label="Client"
                  name="client"
                  value={formData.client}
                  onChange={(value) => setFormData({...formData, client: value})}
                  suggestions={allClients}
                  placeholder="Search for existing client or enter new"
                  required={true}
                  isNewMode={formData.isNewClient}
                  onToggleNewMode={() => setFormData({...formData, isNewClient: !formData.isNewClient, client: ""})}
                  newModeText="You are adding a new client"
                  selectExistingText="Select existing client"
                  addNewText="Add new client"
                />

                <SearchableDropdown
                  label="Role"
                  name="role"
                  value={formData.role}
                  onChange={(value) => setFormData({...formData, role: value})}
                  suggestions={allRoles}
                  placeholder="Search for existing role or enter new"
                  required={true}
                  isNewMode={formData.isNewRole}
                  onToggleNewMode={() => setFormData({...formData, isNewRole: !formData.isNewRole, role: ""})}
                  newModeText="You are adding a new role"
                  selectExistingText="Select existing role"
                  addNewText="Add new role"
                />

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="skills"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Skills:
                  </label>
                  <input
                    type="text"
                    id="skills"
                    name="skills"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.skills}
                    onChange={handleInputChange}
                    placeholder="e.g., JavaScript, React, Node.js"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="numberOfPositions"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * No Of Positions:
                  </label>
                  <input
                    type="number"
                    id="numberOfPositions"
                    name="numberOfPositions"
                    required
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.numberOfPositions}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="country"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Country:
                  </label>
                  <select
                    id="country"
                    name="country"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.country}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Country</option>
                    {countries.map((country, index) => (
                      <option key={index} value={country}>
                        {country}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="place"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Place:
                  </label>
                  <input
                    type="text"
                    id="place"
                    name="place"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.place}
                    onChange={handleInputChange}
                    placeholder="e.g., Mumbai, New York, Remote"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="jobType"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Job Type:
                  </label>
                  <select
                    id="jobType"
                    name="jobType"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.jobType}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Job Type</option>
                    {jobTypes.map((type, index) => (
                      <option key={index} value={type}>
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="modeOfWork"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Mode of Work:
                  </label>
                  <select
                    id="modeOfWork"
                    name="modeOfWork"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.modeOfWork}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Mode of Work</option>
                    {modesOfWork.map((mode, index) => (
                      <option key={index} value={mode}>
                        {mode}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="minExperience"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Minimum Experience:
                  </label>
                  <input
                    type="number"
                    id="minExperience"
                    name="minExperience"
                    required
                    min="0"
                    step="0.5"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.minExperience}
                    onChange={handleInputChange}
                    placeholder="Years"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="maxExperience"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Maximum Experience:
                  </label>
                  <input
                    type="number"
                    id="maxExperience"
                    name="maxExperience"
                    required
                    min="0"
                    step="0.5"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.maxExperience}
                    onChange={handleInputChange}
                    placeholder="Years"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="currency"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Currency:
                  </label>
                  <select
                    id="currency"
                    name="currency"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.currency}
                    onChange={handleInputChange}
                  >
                    {currencies.map((currency) => (
                      <option key={currency.code} value={currency.code}>
                        {currency.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="minBudget"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Minimum Budget:
                  </label>
                  <div className="flex rounded-md shadow-sm w-full">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                      {currencies.find(c => c.code === formData.currency)?.symbol || "₹"}
                    </span>
                    <input
                      type="number"
                      id="minBudget"
                      name="minBudget"
                      required
                      min="0"
                      className="flex-1 w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.minBudget}
                      onChange={handleInputChange}
                      placeholder="Numeric"
                    />
                  </div>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="maxBudget"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Maximum Budget:
                  </label>
                  <div className="flex rounded-md shadow-sm w-full">
                    <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                      {currencies.find(c => c.code === formData.currency)?.symbol || "₹"}
                    </span>
                    <input
                      type="number"
                      id="maxBudget"
                      name="maxBudget"
                      required
                      min="0"
                      className="flex-1 w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      value={formData.maxBudget}
                      onChange={handleInputChange}
                      placeholder="Numeric"
                    />
                  </div>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="shiftTimings"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Shift Timings:
                  </label>
                  <select
                    id="shiftTimings"
                    name="shiftTimings"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.shiftTimings}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Shift Timings</option>
                    {shiftTimings.map((shift, index) => (
                      <option key={index} value={shift}>
                        {shift}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="noticePeriod"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Notice Period:
                  </label>
                  <input
                    type="text"
                    id="noticePeriod"
                    name="noticePeriod"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={formData.noticePeriod}
                    onChange={handleInputChange}
                    placeholder="e.g., 30 days, 60 days"
                  />
                </div>

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="jobStatus"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Job Status:
                  </label>
                  <select
                    id="jobStatus"
                    name="jobStatus"
                    required
                    className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    value={formData.jobStatus}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Job Status</option>
                    {jobStatuses.map((status, index) => (
                      <option key={index} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                </div>

                <SearchableDropdown
                  label="Recruiter"
                  name="recruiter"
                  value={formData.recruiter}
                  onChange={(value) => setFormData({...formData, recruiter: value})}
                  suggestions={allRecruiters}
                  placeholder="Search for existing recruiter or enter new"
                  required={true}
                  isNewMode={false} // Always allow typing for recruiters
                  onToggleNewMode={() => {}} // No toggle needed for recruiters
                  newModeText=""
                  selectExistingText=""
                  addNewText=""
                />

                <div className="space-y-1 w-full">
                  <label
                    htmlFor="detailedJD"
                    className="block text-sm font-medium text-gray-700"
                  >
                    * Detailed JD:
                  </label>
                  <div className="flex items-center space-x-3">
                    <label
                      htmlFor="jd-upload"
                      className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Choose file
                      <input
                        id="jd-upload"
                        name="jd-upload"
                        type="file"
                        className="sr-only"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileChange}
                      />
                    </label>
                    <span className="text-sm text-gray-500 flex-1">
                      {formData.detailedJD
                        ? formData.detailedJD.name
                        : "No file chosen"}
                    </span>
                    {formData.detailedJD && (
                      <button
                        type="button"
                        className="text-red-600 hover:text-red-800 text-sm"
                        onClick={() =>
                          setFormData({ ...formData, detailedJD: null })
                        }
                      >
                        Clear
                      </button>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-1 w-full">
                <label
                  htmlFor="jobDescription"
                  className="block text-sm font-medium text-gray-700"
                >
                  Detailed Job Description:
                </label>
                <textarea
                  id="jobDescription"
                  name="jobDescription"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={formData.jobDescription}
                  onChange={handleInputChange}
                  placeholder="Enter detailed job description here..."
                />
              </div>
            </div>
          </form>
        </div>
      </div>
        {/* Submit Button outside form (sticky to bottom of white panel) */}
        <div className="w-full p-4 flex justify-end bg-white shadow-top sticky bottom-0 border-t border-gray-200">
          <button
            type="button"
            onClick={handleSubmit}
            className="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Post Job
          </button>
        </div>
      </div>
    </div>
  );
}
