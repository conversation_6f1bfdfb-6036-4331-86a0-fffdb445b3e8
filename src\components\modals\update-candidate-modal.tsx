import { useState } from "react";
import { X, ChevronDown } from "lucide-react";
import { type Candidate } from "@/types/candidate";

interface UpdateCandidateModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidate: Candidate | null;
  onUpdate: (candidateId: string, status: string, comment: string) => void;
}

const STATUS_OPTIONS = [
  "Peer Review",
  "Internal Screening – In Progress",
  "Internal Screening – Rejected",
  "Internal Interview – In Progress",
  "Internal Interview – Rejected",
  "Client Screening – In Progress",
  "Client Screening – Rejected",
  "Client – Duplicate Profile",
  "Client Interview – In Progress",
  "Client Interview – Rejected",
  "Client Interview – HR Round",
  "Client Interview – Managerial Round",
  "Interview Reschedule - Client",
  "Interview Reschedule - Candidate",
  "Interview No-show",
  "Offer – Released",
  "Offer – Declined by Candidate",
  "Offered – Yet to Join",
  "Onboarded",
  "Position – On Hold",
  "Profile – On Hold",
  "Candidate – Dropped",
];

export function UpdateCandidateModal({
  isOpen,
  onClose,
  candidate,
  onUpdate,
}: UpdateCandidateModalProps) {
  const [selectedStatus, setSelectedStatus] = useState(candidate?.status || "");
  const [comment, setComment] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  if (!isOpen || !candidate) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedStatus) {
      onUpdate(candidate.id.toString(), selectedStatus, comment);
      onClose();
      setComment("");
    }
  };

  const handleStatusSelect = (status: string) => {
    setSelectedStatus(status);
    setIsDropdownOpen(false);
  };

  const getAvailableStatuses = () => {
    // Filter statuses based on current status (similar to your logic)
    if (
      candidate.status === "PR-Rejected" ||
      candidate.status === "SCREENING" ||
      candidate.status === "Internal Screening" ||
      candidate.status === "Internal Screening – In Progress"
    ) {
      return STATUS_OPTIONS;
    }
    return STATUS_OPTIONS.filter((status) => status !== "Peer Review");
  };

  return (
    <div className="fixed inset-0 bg-white/40 backdrop-saturate-150 flex items-center justify-center z-50 p-4 transition-all">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center bg-blue-600 text-white justify-between p-3">
          <h2 className="text-xl font-semibold">Update Candidate Status</h2>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Candidate Info */}
          <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <div className="text-sm text-gray-900 font-semibold">
                {candidate.firstName} {candidate.lastName}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mobile
              </label>
              <div className="text-sm text-gray-900">{candidate.phone}</div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <div className="text-sm text-gray-900">{candidate.email}</div>
            </div>
          </div>

          {/* Current Status */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Candidate Current Status
            </label>
            <div className="relative">
              <button
                type="button"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="w-full px-4 py-3 text-left bg-white border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 flex items-center justify-between"
              >
                <span className="text-sm">
                  Level: {selectedStatus || "Select Status"}
                </span>
                <ChevronDown
                  className={`h-4 w-4 transition-transform ${
                    isDropdownOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              {isDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {getAvailableStatuses().map((status) => (
                    <button
                      key={status}
                      type="button"
                      onClick={() => handleStatusSelect(status)}
                      className="w-full px-4 py-2 text-left text-sm hover:bg-blue-50 hover:text-blue-700 transition-colors border-b border-gray-100 last:border-b-0"
                    >
                      {status}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Comment */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Comment
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              placeholder="Add your comment here..."
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              Close
            </button>
            <button
              type="submit"
              disabled={!selectedStatus}
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Update Candidate
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
