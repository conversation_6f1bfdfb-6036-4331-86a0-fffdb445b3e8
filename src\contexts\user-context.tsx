import React, { createContext, useContext, useState } from "react";
import { AuthService, LoginCredentials } from "@/services/auth";

// Define user roles
export type UserRole = "manager" | "recruiter" | null;

// Define user context type
interface UserContextType {
  isAuthenticated: boolean;
  userRole: UserRole;
  userName: string;
  name: string;
  userEmail: string;
  userId: string;
  peerStatus: boolean;
  login: (credentials: LoginCredentials, role: UserRole) => Promise<void>;
  logout: () => void;
}

// Create context with default values
const UserContext = createContext<UserContextType>({
  isAuthenticated: false,
  userRole: null,
  userName: "",
  name: "",
  userEmail: "",
  userId: "",
  peerStatus: false,
  login: async () => {},
  logout: () => {},
});

// Hook to use the user context
export const useUser = () => useContext(UserContext);

// Provider component
export const UserProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    localStorage.getItem("isAuthenticated") === "true"
  );
  const [userRole, setUserRole] = useState<UserRole>(
    (localStorage.getItem("userRole") as UserRole) || null
  );
  const [userName, setUserName] = useState<string>(
    localStorage.getItem("userName") || ""
  );
  const [userEmail, setUserEmail] = useState<string>(
    localStorage.getItem("userEmail") || ""
  );
  const [userId, setUserId] = useState<string>(
    localStorage.getItem("userId") || ""
  );
  const [name, setName] = useState<string>(localStorage.getItem("name") || "");
  const [peerStatus, setPeerStatus] = useState<boolean>(
    localStorage.getItem("peerStatus") === "true"
  );

  // Login function
  const login = async (
    credentials: LoginCredentials,
    role: UserRole
  ): Promise<void> => {
    if (!role) {
      throw new Error("Role is required for login");
    }

    try {
      // Call the authentication API
      const response = await AuthService.login(credentials, role);

      if (response.status !== "success") {
        throw new Error(response.message || "Login failed");
      }

      // Set user data from API response
      if (response.email && response.user_id) {
        setIsAuthenticated(true);
        setUserRole(role);
        setUserEmail(response.email);
        setUserName(credentials.username);
        setUserId(response.user_id.toString());
        setName(response.name || "");
        setPeerStatus(response.peer_status || false);

        // Store in localStorage
        localStorage.setItem("isAuthenticated", "true");
        localStorage.setItem("userRole", role);
        localStorage.setItem("userName", credentials.username);
        localStorage.setItem("userEmail", response.email);
        localStorage.setItem("userId", response.user_id.toString());
        localStorage.setItem("name", response.name || "");
        localStorage.setItem(
          "peerStatus",
          (response.peer_status || false).toString()
        );

        if (response.token) {
          localStorage.setItem("authToken", response.token);
        }
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  };

  // Logout function
  const logout = () => {
    setIsAuthenticated(false);
    setUserRole(null);
    setUserName("");
    setUserEmail("");
    setUserId("");
    setName("");
    setPeerStatus(false);

    // Clear localStorage
    localStorage.removeItem("isAuthenticated");
    localStorage.removeItem("userRole");
    localStorage.removeItem("userName");
    localStorage.removeItem("userEmail");
    localStorage.removeItem("userId");
    localStorage.removeItem("authToken");
    localStorage.removeItem("name");
    localStorage.removeItem("peerStatus");
  };

  // Context value
  const value = {
    isAuthenticated,
    userRole,
    userName,
    userEmail,
    userId,
    name,
    peerStatus,
    login,
    logout,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};
