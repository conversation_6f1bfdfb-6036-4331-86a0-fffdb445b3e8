// API service for HR Portal
import { config } from '@/config/env';

const API_BASE_URL = config.apiBaseUrl;

// Types for API requests and responses
export interface ApiRequest {
  user_id: string;
  user_type: string;
  user_name: string;
  page_no: number;
}

export interface ApiUser {
  id: number;
  name: string;
  user_type: string;
  email: string;
  user_name: string;
}

export interface ApiCandidate {
  id: number;
  job_id: number;
  name: string;
  mobile: string;
  email: string;
  client: string;
  current_company: string;
  position: string;
  profile: string;
  current_job_location: string;
  preferred_job_location: string;
  qualifications: string;
  experience: string;
  relevant_experience: string;
  current_ctc: string;
  expected_ctc: string;
  notice_period: string;
  linkedin: string;
  reason_for_job_change: string;
  holding_offer: string;
  recruiter: string;
  management: string | null;
  status: string;
  remarks: string;
  skills: string;
  serving_notice_period: string;
  period_of_notice: string;
  last_working_date: string | null;
  total_offers: number | null;
  highest_package_in_lpa: number | null;
  buyout: boolean;
  date_created: string;
  data_updated_date: string;
  resume_present: boolean;
  comments: Record<string, unknown>;
  peer_reviewer_level1: string | null;
  peer_reviewer_level2: string | null;
  remind_lwd: boolean;
  user_id: number;
}

export interface ApiResponse {
  candidates: ApiCandidate[];
  name: string;
  user: ApiUser;
}

// Types for Active Users API
export interface ActiveUser {
  client: string | null;
  created_by: string | null;
  deactivated_by: string | null;
  email: string;
  filename: string;
  id: number;
  image_deleted: boolean;
  is_active: boolean;
  is_verified: boolean;
  name: string;
  otp: string;
  password: string;
  peer_reviewer_status: boolean;
  registration_completed: string;
  user_type: string;
  username: string;
}

export interface ActiveUsersResponse {
  active_users_manager: ActiveUser[];
  active_users_recruiter: ActiveUser[];
}

// Request body for updating user status
export interface UpdateUserStatusRequest {
  user_name: string;
  new_status: boolean;
}

// Types for Peer Assigned Profiles API
export interface PeerAssignedCandidate {
  buyout: boolean;
  client: string;
  comments: Record<string, Array<{
    comment: string;
    timestamp: string;
  }>>;
  current_company: string;
  current_ctc: string;
  current_job_location: string;
  data_updated_date: string;
  date_created: string;
  email: string;
  expected_ctc: string;
  experience: string;
  highest_package_in_lpa: number | null;
  holding_offer: string;
  id: number;
  job_id: number;
  last_working_date: string | null;
  linkedin: string;
  management: string | null;
  mobile: string;
  name: string;
  notice_period: string;
  peer_reviewer_level1: string | null;
  peer_reviewer_level2: string | null;
  period_of_notice: string | null;
  position: string;
  preferred_job_location: string;
  profile: string;
  qualifications: string;
  reason_for_job_change: string;
  recruiter: string;
  relevant_experience: string;
  remarks: string;
  remind_lwd: boolean | null;
  resume_present: boolean;
  serving_notice_period: string;
  skills: string;
  status: string;
  total_offers: number | null;
  user_id: number;
}

export interface PeerAssignedProfilesResponse {
  candidates: PeerAssignedCandidate[];
}

export interface PeerAssignedProfilesRequest {
  recruiter: string;
}

// Types for Recruiter Candidates API
export interface RecruiterCandidatesRequest {
  user_name: string;
}

export interface RecruiterCandidate {
  client: string;
  id: number;
  management: string | null;
  profile: string;
  recruiter: string;
  status: string;
  username: string;
}

export interface RecruiterCandidatesResponse {
  candidates: RecruiterCandidate[];
}

// Types for Candidate Analysis API
export interface CandidateAnalysisRequest {
  user_id: string;
  job_id: string;
  resume: string; // Base64 encoded resume
}

export interface CandidateAnalysisResponse {
  analyze_candidate_profile_response: Array<{
    Experience: string;
    "Relevance Score": number;
    "Skill/Domain": string;
  }>;
  candidate_learning_response: {
    Certifications: string[];
    "Company Names": string[];
    "Skills/Domain": string[];
    "Technologies Used": Record<string, string[]>;
    "Working Periods": Record<string, unknown>;
  };
  candidate_learning_textual_representation: {
    BulletPoints: Record<string, string>;
    SummaryParagraph: string;
    Tags: string[];
  };
  career_progress_response: Array<{
    Company: string;
    "From Date": string;
    Location: string;
    Project: string;
    Title: string;
    "To Date": string;
    "Total Duration of Work": string;
  }>;
  expertise_response: {
    categories: Array<{
      Category: string;
      Items: string[];
    }>;
    domains: Array<{
      Description: string;
      Domain: string;
    }>;
  };
  job_info_response: {
    Candidate: string[];
    "Candidate Experience": string[];
    "Candidate Experience Percentage": string[];
    "Candidate Maximum Budget": string[];
    "Candidate Minimum Budget": string[];
    "Job Description Max Experience": string[];
    "Job Description Max Package (LPA)": string[];
    "Job Description Min Experience": string[];
    "Job Description Min Package (LPA)": string[];
    "Job Description Skills": string[];
    "Job Description Skills Count": string[];
    "Matching Skills": string[];
    "Resume Skills": string[];
    "Resume Skills Count": string[];
    "Skills Matching Percentage": string[];
  };
  user_id: string;
}

// Types for View All Jobs API
export interface Job {
  id: number;
  job_id: string;
  client: string;
  role: string;
  positions: number;
  status: string;
  posted_by: string;
  recruiter: string;
  date_posted: string;
  job_description?: string;
}

// Types for Profile Picture API
export interface UploadImageRequest {
  image: string; // Base64 encoded image data
}

export interface UploadImageResponse {
  message: string;
  success: boolean;
}



// API service class
export class ApiService {
  private static async makeRequest<T>(
    endpoint: string,
    data: ApiRequest
  ): Promise<T> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Fetch candidates from the API
  static async fetchCandidates(
    userId: string,
    userType: string,
    userName: string,
    pageNo: number = 1
  ): Promise<ApiResponse> {
    const requestData: ApiRequest = {
      user_id: userId,
      user_type: userType,
      user_name: userName,
      page_no: pageNo,
    };

    console.log('Fetching candidates with request:', requestData);
    const response = await this.makeRequest<ApiResponse>('/redisdashboard', requestData);
    console.log('API response received:', response);

    return response;
  }

  // Fetch active users from the API
  static async fetchActiveUsers(): Promise<ActiveUsersResponse> {
    const body = {
      "user_name": localStorage.getItem('userName') || '',
      "new_status": false
    };
    try {
      const response = await fetch(`${API_BASE_URL}/active_users`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Failed to fetch active users:', error);
      throw error;
    }
  }

  // Update user status (verify, deactivate, peer reviewer)
  static async updateUserStatus(
    userName: string,
    newStatus: boolean,
    statusType: 'verify' | 'deactivate' | 'peer_reviewer'
  ): Promise<unknown> {
    try {
      const requestBody: UpdateUserStatusRequest = {
        user_name: userName,
        new_status: newStatus,
      };

      // Determine the endpoint based on status type
      let endpoint = '';
      switch (statusType) {
        case 'verify':
          endpoint = '/update_user_verification';
          break;
        case 'deactivate':
          endpoint = '/update_user_activation';
          break;
        case 'peer_reviewer':
          endpoint = '/update_peer_reviewer_status';
          break;
        default:
          throw new Error('Invalid status type');
      }

      console.log(`Updating user ${statusType} status:`, requestBody);

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log(`User ${statusType} status update response:`, result);
      return result;
    } catch (error) {
      console.error(`Failed to update user ${statusType} status:`, error);
      throw error;
    }
  }

  // Fetch peer assigned profiles from the API
  static async fetchPeerAssignedProfiles(recruiterName: string): Promise<PeerAssignedProfilesResponse> {
    try {
      const requestBody: PeerAssignedProfilesRequest = {
        recruiter: recruiterName,
      };

      console.log('Fetching peer assigned profiles for recruiter:', recruiterName);

      const response = await fetch(`${API_BASE_URL}/candidates_pending_peer_approval`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Peer assigned profiles API response received:', result);
      return result;
    } catch (error) {
      console.error('Failed to fetch peer assigned profiles:', error);
      throw error;
    }
  }

  // Fetch recruiter's candidates from the API
  static async fetchRecruiterCandidates(recruiterName: string): Promise<RecruiterCandidate[]> {
    try {
      const requestBody: RecruiterCandidatesRequest = {
        user_name: recruiterName,
      };

      console.log('Fetching recruiter candidates with request:', requestBody);

      const response = await fetch(`${API_BASE_URL}/get_recruiters_candidate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: RecruiterCandidate[] = await response.json();
      console.log('Recruiter candidates API response received:', result);
      return result;
    } catch (error) {
      console.error('Failed to fetch recruiter candidates:', error);
      throw error;
    }
  }

  // Analyze candidate profile
  static async analyzeCandidateProfile(
    userId: string,
    jobId: string,
    resumeBase64: string
  ): Promise<CandidateAnalysisResponse> {
    try {
      const requestBody: CandidateAnalysisRequest = {
        user_id: userId,
        job_id: jobId,
        resume: resumeBase64,
      };

      console.log('Analyzing candidate profile with request:', { ...requestBody, resume: '[BASE64_DATA]' });

      const response = await fetch(`${API_BASE_URL}/candidate_over_view`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: CandidateAnalysisResponse = await response.json();
      console.log('Candidate analysis API response received:', result);
      return result;
    } catch (error) {
      console.error('Failed to analyze candidate profile:', error);
      throw error;
    }
  }

  // Get user profile image
  static async getUserImage(userId: string): Promise<string | null> {
    try {
      console.log('Fetching user image for user ID:', userId);

      const response = await fetch(`${API_BASE_URL}/user_image/${userId}`, {
        method: 'GET',
      });

      if (response.status === 404) {
        // No image found for user
        console.log('No profile image found for user:', userId);
        return null;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Convert binary image data to base64
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Failed to fetch user image:', error);
      throw error;
    }
  }

  // Upload user profile image
  static async uploadUserImage(userId: string, imageData: string): Promise<UploadImageResponse> {
    try {
      const requestBody: UploadImageRequest = {
        image: imageData,
      };

      console.log('Uploading user image for user ID:', userId);

      const response = await fetch(`${API_BASE_URL}/upload_user_image/${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: UploadImageResponse = await response.json();
      console.log('User image upload response:', result);
      return result;
    } catch (error) {
      console.error('Failed to upload user image:', error);
      throw error;
    }
  }

}

// Helper function to convert API candidate to local Candidate type
export function convertApiCandidateToLocal(apiCandidate: ApiCandidate) {
  return {
    id: apiCandidate.id,
    jobId: apiCandidate.job_id.toString(),
    firstName: apiCandidate.name.split(' ')[0] || '',
    lastName: apiCandidate.name.split(' ').slice(1).join(' ') || '',
    email: apiCandidate.email,
    phone: apiCandidate.mobile,
    client: apiCandidate.client,
    profile: apiCandidate.profile,
    skills: apiCandidate.skills,
    status: mapApiStatusToLocal(apiCandidate.status),
    appliedDate: apiCandidate.date_created,
    source: 'API', // Default value since not provided in API
    experience: parseFloat(apiCandidate.experience) || 0,
    education: apiCandidate.qualifications,
    location: apiCandidate.preferred_job_location,
    salary: `${apiCandidate.current_ctc} - ${apiCandidate.expected_ctc}`,
    notes: apiCandidate.remarks || '',
    lastUpdated: apiCandidate.data_updated_date,
    comment: apiCandidate.reason_for_job_change || '',
    peerReviewer: apiCandidate.peer_reviewer_level1 || apiCandidate.peer_reviewer_level2 || '',
    recruiter: apiCandidate.recruiter,
  };
}

// Map API status to local status

function mapApiStatusToLocal(apiStatus: string): "New" | "Screening" | "Interview" | "Offer" | "Rejected" | "Hired" {
  const statusMap: Record<string, "New" | "Screening" | "Interview" | "Offer" | "Rejected" | "Hired"> = {
    'Internal Screening': 'Screening',
    'New': 'New',
    'Interview': 'Interview',
    'Offer': 'Offer',
    'Rejected': 'Rejected',
    'Hired': 'Hired',
    'Screening': 'Screening',
  };

  return statusMap[apiStatus] || 'New';
}
