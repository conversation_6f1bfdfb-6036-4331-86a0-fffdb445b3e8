import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { type Candidate } from "@/types/candidate";

interface EditCandidateModalProps {
  candidate: Candidate | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedCandidate: Candidate) => void;
}

export function EditCandidateModal({
  candidate,
  isOpen,
  onClose,
  onSave,
}: EditCandidateModalProps) {
  const [formData, setFormData] = useState({
    // Basic Information
    jobId: "",
    mobile: "",
    client: "",
    skills: "",
    position: "",
    resume: null as File | null,
    preferredJobLocation: "",
    months1: "",
    months2: "",
    currentCTC: "",
    expectedCTC: "",
    servingNoticePeriod: "",
    noticePeriod: "",
    linkedinProfile: "",
    additionalFiles: null as File | null,

    // Right side fields
    name: "",
    email: "",
    profile: "",
    currentCompany: "",
    reasonForJobChange: "",
    currentJobLocation: "",
    totalExperience: "",
    relevantExperience: "",
    currentCTCCurrencyType: "USD",
    expectedCTCCurrencyType: "USD",
    qualifications: "",
    holdingOffer: "",
    buyout: "No",
    remarks: "",
  });

  useEffect(() => {
    if (isOpen && candidate) {
      // Pre-populate form with candidate data
      setFormData({
        jobId: candidate.jobId || "",
        mobile: candidate.phone || "",
        client: candidate.client || "",
        skills: candidate.skills || "",
        position: candidate.profile || "",
        resume: null,
        preferredJobLocation: candidate.location || "",
        months1: "",
        months2: "",
        currentCTC: candidate.salary || "",
        expectedCTC: "",
        servingNoticePeriod: "No",
        noticePeriod: "any",
        linkedinProfile: "",
        additionalFiles: null,
        name: `${candidate.firstName} ${candidate.lastName}`,
        email: candidate.email || "",
        profile: candidate.profile || "",
        currentCompany: "",
        reasonForJobChange: "",
        currentJobLocation: candidate.location || "",
        totalExperience: candidate.experience?.toString() || "",
        relevantExperience: "",
        currentCTCCurrencyType: "USD",
        expectedCTCCurrencyType: "USD",
        qualifications: candidate.education || "",
        holdingOffer: "",
        buyout: "No",
        remarks: candidate.notes || "",
      });
    }
  }, [isOpen, candidate]);

  // Handle escape key press
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    let previousOverflow = "";
    if (isOpen) {
      document.addEventListener("keydown", handleEscapeKey);
      previousOverflow = document.body.style.overflow;
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
      document.body.style.overflow = previousOverflow;
    };
  }, [isOpen, onClose]);

  if (!isOpen || !candidate) return null;

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleFileChange = (field: string, file: File | null) => {
    setFormData((prev) => ({ ...prev, [field]: file }));
  };

  const handleUpdate = () => {
    // Create updated candidate object
    const updatedCandidate: Candidate = {
      ...candidate,
      jobId: formData.jobId,
      phone: formData.mobile,
      client: formData.client,
      skills: formData.skills,
      profile: formData.position,
      location: formData.currentJobLocation,
      salary: formData.currentCTC,
      experience: parseFloat(formData.totalExperience) || 0,
      education: formData.qualifications,
      notes: formData.remarks,
      email: formData.email,
      firstName: formData.name.split(" ")[0] || "",
      lastName: formData.name.split(" ").slice(1).join(" ") || "",
      lastUpdated: new Date().toISOString().split("T")[0],
    };

    onSave(updatedCandidate);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-white/40 backdrop-saturate-150 flex items-center justify-center z-50 p-4 transition-all">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-blue-600 text-white p-3 relative">
          <h2 className="text-xl font-semibold text-center">
            Edit Candidate Details
          </h2>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Job ID:
                </label>
                <input
                  type="text"
                  value={formData.jobId}
                  onChange={(e) => handleInputChange("jobId", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Mobile:
                </label>
                <input
                  type="tel"
                  value={formData.mobile}
                  onChange={(e) => handleInputChange("mobile", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Client:
                </label>
                <input
                  type="text"
                  value={formData.client}
                  onChange={(e) => handleInputChange("client", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Skills:
                </label>
                <textarea
                  value={formData.skills}
                  onChange={(e) => handleInputChange("skills", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position:
                </label>
                <input
                  type="text"
                  value={formData.position}
                  onChange={(e) =>
                    handleInputChange("position", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Resume: (previously uploaded)
                </label>
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) =>
                    handleFileChange("resume", e.target.files?.[0] || null)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Job Location:
                </label>
                <input
                  type="text"
                  value={formData.preferredJobLocation}
                  onChange={(e) =>
                    handleInputChange("preferredJobLocation", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Months:
                  </label>
                  <select
                    value={formData.months1}
                    onChange={(e) =>
                      handleInputChange("months1", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select</option>
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {i + 1}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Months:
                  </label>
                  <select
                    value={formData.months2}
                    onChange={(e) =>
                      handleInputChange("months2", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select</option>
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {i + 1}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  *Current CTC:
                </label>
                <input
                  type="text"
                  value={formData.currentCTC}
                  onChange={(e) =>
                    handleInputChange("currentCTC", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  *Expected CTC:
                </label>
                <input
                  type="text"
                  value={formData.expectedCTC}
                  onChange={(e) =>
                    handleInputChange("expectedCTC", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  *Serving Notice Period:
                </label>
                <select
                  value={formData.servingNoticePeriod}
                  onChange={(e) =>
                    handleInputChange("servingNoticePeriod", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="No">No</option>
                  <option value="Yes">Yes</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  *Notice period:
                </label>
                <select
                  value={formData.noticePeriod}
                  onChange={(e) =>
                    handleInputChange("noticePeriod", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="any">any</option>
                  <option value="immediate">Immediate</option>
                  <option value="15days">15 days</option>
                  <option value="30days">30 days</option>
                  <option value="60days">60 days</option>
                  <option value="90days">90 days</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Linkedin Profile:
                </label>
                <input
                  type="url"
                  value={formData.linkedinProfile}
                  onChange={(e) =>
                    handleInputChange("linkedinProfile", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Additional Files:
                </label>
                <input
                  type="file"
                  multiple
                  onChange={(e) =>
                    handleFileChange(
                      "additionalFiles",
                      e.target.files?.[0] || null
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Name:
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Email:
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Profile:
                </label>
                <input
                  type="text"
                  value={formData.profile}
                  onChange={(e) => handleInputChange("profile", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current Company:
                </label>
                <input
                  type="text"
                  value={formData.currentCompany}
                  onChange={(e) =>
                    handleInputChange("currentCompany", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reason for Job Change:
                </label>
                <input
                  type="text"
                  value={formData.reasonForJobChange}
                  onChange={(e) =>
                    handleInputChange("reasonForJobChange", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current Job Location:
                </label>
                <input
                  type="text"
                  value={formData.currentJobLocation}
                  onChange={(e) =>
                    handleInputChange("currentJobLocation", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  *Total Experience (Years):
                </label>
                <select
                  value={formData.totalExperience}
                  onChange={(e) =>
                    handleInputChange("totalExperience", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select</option>
                  <option value="Fresher">Fresher</option>
                  {Array.from({ length: 20 }, (_, i) => (
                    <option key={i + 1} value={`${i + 1} years`}>
                      {i + 1} years
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Relevant Experience (Years):
                </label>
                <select
                  value={formData.relevantExperience}
                  onChange={(e) =>
                    handleInputChange("relevantExperience", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select</option>
                  <option value="Fresher">Fresher</option>
                  {Array.from({ length: 20 }, (_, i) => (
                    <option key={i + 1} value={`${i + 1} years`}>
                      {i + 1} years
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Current CTC Currency Type:
                </label>
                <select
                  value={formData.currentCTCCurrencyType}
                  onChange={(e) =>
                    handleInputChange("currentCTCCurrencyType", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="USD">USD</option>
                  <option value="INR">INR</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  * Expected CTC Currency Type:
                </label>
                <select
                  value={formData.expectedCTCCurrencyType}
                  onChange={(e) =>
                    handleInputChange("expectedCTCCurrencyType", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="USD">USD</option>
                  <option value="INR">INR</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Qualifications:
                </label>
                <input
                  type="text"
                  value={formData.qualifications}
                  onChange={(e) =>
                    handleInputChange("qualifications", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  *Holding Offer:
                </label>
                <input
                  type="text"
                  value={formData.holdingOffer}
                  onChange={(e) =>
                    handleInputChange("holdingOffer", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Buyout:
                </label>
                <select
                  value={formData.buyout}
                  onChange={(e) => handleInputChange("buyout", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="No">No</option>
                  <option value="Yes">Yes</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Remarks:
                </label>
                <textarea
                  value={formData.remarks}
                  onChange={(e) => handleInputChange("remarks", e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-center">
          <button
            onClick={handleUpdate}
            className="px-8 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors font-medium"
          >
            Update
          </button>
        </div>
      </div>
    </div>
  );
}
