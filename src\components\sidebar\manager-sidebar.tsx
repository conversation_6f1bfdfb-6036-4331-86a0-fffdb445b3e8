import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useLogoutConfirmation } from "@/hooks/use-logout-confirmation";
import { RoleBadge } from "@/components/ui/role-badge";
import {
  Calendar,
  Home,
  ClipboardList,
  BarChart2,
  UserCheck,
  Lock,
  LogOut,
  Briefcase,
  UserPlus,
  UserCog,
  Users,
  ArrowRightLeft,
} from "lucide-react";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface MenuSection {
  title?: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    items: [{ title: "Dashboard", url: "/manager/dashboard", icon: Home }],
  },
  {
    title: "RECRUITMENT",
    items: [
      { title: "Job Listing", url: "/manager/job-listing", icon: Briefcase },
      {
        title: "Job Assignments",
        url: "/manager/job-assignments",
        icon: ClipboardList,
      },
    ],
  },
  {
    title: "CANDIDATES",
    items: [
      {
        title: "Register Candidate",
        url: "/register-candidate",
        icon: UserPlus,
      },
      {
        title: "Peer Assigned Profiles",
        url: "/manager/peer-assigned-profiles",
        icon: UserCheck,
      },
      {
        title: "Profile Transfer",
        url: "/manager/profile-transfer",
        icon: ArrowRightLeft,
      },
    ],
  },
  {
    title: "GENERAL",
    items: [
      { title: "Analytics", url: "/manager/analytics", icon: BarChart2 },
      { title: "User Accounts", url: "/manager/user-accounts", icon: UserCog },
      { title: "Calendar", url: "/calendar", icon: Calendar },
      { title: "Help and Support", url: "/help-and-support", icon: Users },
      { title: "Change Password", url: "/change-password", icon: Lock },
      { title: "Logout", url: "/logout", icon: LogOut },
    ],
  },
];

export function ManagerSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;
  const { handleLogoutClick, LogoutDialog } = useLogoutConfirmation();

  return (
    <Sidebar className="bg-[var(--sidebar)] text-[var(--sidebar-foreground)] w-52 border-r border-[var(--sidebar-border)]">
      <SidebarHeader className="p-4 border-b border-[var(--sidebar-border)]">
        <div className="flex items-center space-x-3">
          <h2 className="text-lg font-semibold text-white">Manager</h2>
          <RoleBadge />
        </div>
      </SidebarHeader>

      <SidebarContent className=" flex-1 overflow-y-auto">
        {menuSections.map((section, sectionIndex) => (
          <SidebarGroup key={sectionIndex}>
            {section.title && (
              <SidebarGroupLabel className="px-1 py-1 text-xs font-semibold text-[var(--sidebar-text-secondary)] uppercase tracking-wider">
                {section.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu className="space-y-0">
                {section.items.map((item) => {
                  const isActive = currentPath === item.url;
                  const isLogout = item.title === "Logout";

                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild={!isLogout}
                        isActive={isActive}
                        className={`w-full rounded-md px-1 py-1 text-sm font-medium transition-all duration-200 ${
                          isActive
                            ? "bg-[var(--sidebar-primary)] text-[var(--sidebar-primary-foreground)] shadow-sm"
                            : "text-[var(--sidebar-foreground)] hover:bg-[var(--sidebar-hover)] hover:text-[var(--sidebar-primary-foreground)]"
                        }`}
                      >
                        {isLogout ? (
                          <button
                            onClick={handleLogoutClick}
                            className="flex items-center w-full"
                          >
                            <item.icon className="h-4 w-4 mr-4 text-[var(--sidebar-foreground)]" />
                            <span>{item.title}</span>
                          </button>
                        ) : (
                          <Link to={item.url} className="flex items-center">
                            <item.icon
                              className={`h-4 w-4 mr-4 ${
                                isActive
                                  ? "text-[var(--sidebar-primary-foreground)]"
                                  : "text-[var(--sidebar-foreground)]"
                              }`}
                            />
                            <span>{item.title}</span>
                          </Link>
                        )}
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      {/* Logout Confirmation Dialog */}
      <LogoutDialog />
    </Sidebar>
  );
}
