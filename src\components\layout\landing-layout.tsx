import { useState } from "react";
import { ChevronDown, LogOut } from "lucide-react";
import { useUser } from "@/contexts/user-context";
import { useLogoutConfirmation } from "@/hooks/use-logout-confirmation";
import ATS_LOGO from "@/assets/ATS_LOGO.png";

interface LandingLayoutProps {
  children: React.ReactNode;
}

// Landing-specific header without sidebar trigger
function LandingHeader() {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const { userName, userEmail } = useUser();
  const { handleLogoutClick, LogoutDialog } = useLogoutConfirmation();

  const toggleProfileMenu = () => {
    setIsProfileMenuOpen(!isProfileMenuOpen);
  };

  const handleLogoutClickWithClose = () => {
    handleLogoutClick();
    setIsProfileMenuOpen(false);
  };

  return (
    <header className="bg-white border-b border-gray-200 py-1 px-6 flex items-center justify-between shadow-sm">
      {/* Logo Section */}
      <div className="flex items-center">
        <img src={ATS_LOGO} alt="ATS Logo" className="h-12 w-auto mr-4" />
        <div className="text-xl font-bold text-blue-900">
          Makonis TalentTrack Pro
        </div>
      </div>

      {/* Profile Section */}
      <div className="flex items-center space-x-4">
        <div className="relative">
          <button
            onClick={toggleProfileMenu}
            className="flex items-center space-x-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
          >
            <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white overflow-auto">
              <span className="font-medium text-sm">
                {(userName || "U").charAt(0).toUpperCase()}
              </span>
            </div>
            <span className="hidden md:inline-block font-medium text-gray-700">
              {userName || "User"}
            </span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </button>

          {/* Profile Dropdown */}
          {isProfileMenuOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200">
                <p className="text-sm font-medium text-gray-900">
                  {userName || "User"}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {userEmail || "<EMAIL>"}
                </p>
              </div>
              <button
                onClick={handleLogoutClickWithClose}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Logout Confirmation Dialog */}
      <LogoutDialog />
    </header>
  );
}

export function LandingLayout({ children }: LandingLayoutProps) {
  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <LandingHeader />
      <main className="flex-1 overflow-auto">{children}</main>
    </div>
  );
}
