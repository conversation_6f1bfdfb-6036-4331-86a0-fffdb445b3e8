import { useState, useEffect } from "react";
import { ChevronDown, LogOut, Camera } from "lucide-react";
import { useUser } from "@/contexts/user-context";
import { useLogoutConfirmation } from "@/hooks/use-logout-confirmation";
import { ProfilePictureModal } from "@/components/modals/ProfilePictureModal";
import { ApiService } from "@/services/api";
import ATS_LOGO from "@/assets/ATS_LOGO.png";

interface LandingLayoutProps {
  children: React.ReactNode;
}

// Landing-specific header without sidebar trigger
function LandingHeader() {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isProfilePictureModalOpen, setIsProfilePictureModalOpen] = useState(false);
  const [currentUserAvatar, setCurrentUserAvatar] = useState<string | undefined>(() => {
    // Initialize with cached value
    const userId = localStorage.getItem("user_id");
    if (userId) {
      const cachedImage = localStorage.getItem(`user_avatar_${userId}`);
      if (cachedImage && cachedImage !== "null" && cachedImage !== "undefined") {
        return cachedImage;
      }
    }
    return undefined;
  });
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const [error, setError] = useState<string>("");
  const { userName, userEmail } = useUser();
  const { handleLogoutClick, LogoutDialog } = useLogoutConfirmation();

  // Load user profile image on component mount
  useEffect(() => {
    const loadUserImage = async () => {
      const userId = localStorage.getItem("user_id");
      if (!userId) return;

      // If we already have an avatar (from cache), don't fetch again
      if (currentUserAvatar) return;

      // Otherwise, fetch from API
      setIsLoadingImage(true);
      setError("");
      try {
        const imageData = await ApiService.getUserImage(userId);
        if (imageData) {
          setCurrentUserAvatar(imageData);
          localStorage.setItem(`user_avatar_${userId}`, imageData);
        }
      } catch (error) {
        console.error("Failed to load user image:", error);
        setError("Failed to load profile picture");
      } finally {
        setIsLoadingImage(false);
      }
    };

    loadUserImage();
  }, [currentUserAvatar]);

  // Update localStorage when currentUserAvatar changes
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId && currentUserAvatar) {
      localStorage.setItem(`user_avatar_${userId}`, currentUserAvatar);
    }
  }, [currentUserAvatar]);

  const toggleProfileMenu = () => {
    setIsProfileMenuOpen(!isProfileMenuOpen);
  };

  const handleLogoutClickWithClose = () => {
    handleLogoutClick();
    setIsProfileMenuOpen(false);
  };

  const handleChangeProfilePicture = () => {
    setIsProfileMenuOpen(false);
    setIsProfilePictureModalOpen(true);
  };

  const handleSaveProfilePicture = async (imageData: string) => {
    const userId = localStorage.getItem("user_id");
    if (!userId) {
      throw new Error("User ID not found");
    }

    // Store previous avatar for rollback on error
    const previousAvatar = currentUserAvatar;

    try {
      // Optimistic update - update UI immediately
      if (imageData === "") {
        setCurrentUserAvatar(undefined);
        localStorage.removeItem(`user_avatar_${userId}`);
      } else {
        setCurrentUserAvatar(imageData);
        localStorage.setItem(`user_avatar_${userId}`, imageData);
      }

      setError("");

      // Make API call
      await ApiService.uploadUserImage(userId, imageData);

      // Success - the optimistic update is already in place
      console.log("Profile picture updated successfully");
    } catch (error) {
      // Rollback optimistic update on error
      setCurrentUserAvatar(previousAvatar);
      if (previousAvatar) {
        localStorage.setItem(`user_avatar_${userId}`, previousAvatar);
      } else {
        localStorage.removeItem(`user_avatar_${userId}`);
      }
      setError("Failed to update profile picture");
      console.error("Failed to save profile picture:", error);
      throw error;
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 py-1 px-6 flex items-center justify-between shadow-sm">
      {/* Logo Section */}
      <div className="flex items-center">
        <img src={ATS_LOGO} alt="ATS Logo" className="h-12 w-auto mr-4" />
        <div className="text-xl font-bold text-blue-900">
          Makonis TalentTrack Pro
        </div>
      </div>

      {/* Profile Section */}
      <div className="flex items-center space-x-4">
        <div className="relative">
          <button
            onClick={toggleProfileMenu}
            className="flex items-center space-x-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
          >
            <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white overflow-hidden relative">
              {isLoadingImage ? (
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
              ) : currentUserAvatar ? (
                <img
                  src={currentUserAvatar}
                  alt={userName || "User"}
                  className="h-full w-full object-cover"
                />
              ) : (
                <span className="font-medium text-sm">
                  {(userName || "U").charAt(0).toUpperCase()}
                </span>
              )}
              {error && (
                <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full border border-white" title={error}></div>
              )}
            </div>
            <span className="hidden md:inline-block font-medium text-gray-700">
              {userName || "User"}
            </span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </button>

          {/* Profile Dropdown */}
          {isProfileMenuOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-[9999] border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200">
                <p className="text-sm font-medium text-gray-900">
                  {userName || "User"}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {userEmail || "<EMAIL>"}
                </p>
              </div>
              <div className="py-1">
                <button
                  onClick={handleChangeProfilePicture}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Camera className="h-4 w-4 mr-3" />
                  Change Profile
                </button>
                <div className="border-t border-gray-200 my-1"></div>
                <button
                  onClick={handleLogoutClickWithClose}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Logout Confirmation Dialog */}
      <LogoutDialog />

      {/* Profile Picture Modal */}
      <ProfilePictureModal
        isOpen={isProfilePictureModalOpen}
        onClose={() => setIsProfilePictureModalOpen(false)}
        onSave={handleSaveProfilePicture}
        currentImage={currentUserAvatar}
        userName={userName || "User"}
      />
    </header>
  );
}

export function LandingLayout({ children }: LandingLayoutProps) {
  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <LandingHeader />
      <main className="flex-1 overflow-auto">{children}</main>
    </div>
  );
}
