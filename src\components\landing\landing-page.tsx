import React from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>R<PERSON>,
  BarChart3,
  Users,
  FileText,
  Calendar,
  UserPlus,
  ClipboardList,
  TrendingUp,
  Clock,
  CheckCircle,
} from "lucide-react";
import { useUser } from "@/contexts/user-context";

interface QuickNavCard {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  color: string;
}

interface MetricCard {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  trend?: string;
  color: string;
}

export function LandingPage() {
  const navigate = useNavigate();
  const { userRole, userName, name } = useUser();

  const handleGoToDashboard = () => {
    const dashboardPath =
      userRole === "manager" ? "/manager/dashboard" : "/recruiter/dashboard";
    navigate(dashboardPath);
  };

  // Quick navigation cards based on user role
  const getQuickNavCards = (): QuickNavCard[] => {
    const commonCards: QuickNavCard[] = [
      {
        title: "Dashboard",
        description: "View your main dashboard with key metrics and insights",
        icon: BarChart3,
        href:
          userRole === "manager"
            ? "/manager/dashboard"
            : "/recruiter/dashboard",
        color: "bg-blue-500",
      },
      {
        title: "Calendar",
        description: "Schedule and manage your meetings and interviews",
        icon: Calendar,
        href: "/calendar",
        color: "bg-green-500",
      },
      {
        title: "Help & Support",
        description: "Get assistance and find answers to your questions",
        icon: FileText,
        href: "/help-and-support",
        color: "bg-purple-500",
      },
    ];

    if (userRole === "manager") {
      return [
        ...commonCards.slice(0, 1), // Dashboard
        {
          title: "Job Listings",
          description: "Create and manage job postings and requirements",
          icon: ClipboardList,
          href: "/manager/job-listing",
          color: "bg-orange-500",
        },
        {
          title: "Job Assignments",
          description: "Assign jobs to recruiters and track progress",
          icon: Users,
          href: "/manager/job-assignments",
          color: "bg-red-500",
        },
        {
          title: "User Accounts",
          description: "Manage user accounts and permissions",
          icon: UserPlus,
          href: "/manager/user-accounts",
          color: "bg-indigo-500",
        },
        ...commonCards.slice(1), // Calendar, Help & Support
      ];
    } else {
      return [
        ...commonCards.slice(0, 1), // Dashboard
        {
          title: "Requirements",
          description: "View and manage your assigned job requirements",
          icon: ClipboardList,
          href: "/recruiter/requirements",
          color: "bg-orange-500",
        },
        {
          title: "Register Candidate",
          description: "Add new candidates to the system",
          icon: UserPlus,
          href: "/register-candidate",
          color: "bg-green-600",
        },
        {
          title: "Profile Analysis",
          description: "Analyze candidate profiles and match with jobs",
          icon: TrendingUp,
          href: "/recruiter/profile-analysis",
          color: "bg-yellow-500",
        },
        ...commonCards.slice(1), // Calendar, Help & Support
      ];
    }
  };

  // Mock metrics data (in real app, this would come from API)
  const getMetrics = (): MetricCard[] => {
    if (userRole === "manager") {
      return [
        {
          title: "Active Jobs",
          value: 24,
          icon: ClipboardList,
          trend: "+3 this week",
          color: "text-blue-600",
        },
        {
          title: "Total Recruiters",
          value: 12,
          icon: Users,
          trend: "+2 this month",
          color: "text-green-600",
        },
        {
          title: "Pending Assignments",
          value: 8,
          icon: Clock,
          trend: "-2 from yesterday",
          color: "text-orange-600",
        },
        {
          title: "Completed This Month",
          value: 15,
          icon: CheckCircle,
          trend: "+5 from last month",
          color: "text-purple-600",
        },
      ];
    } else {
      return [
        {
          title: "Assigned Requirements",
          value: 6,
          icon: ClipboardList,
          trend: "+1 this week",
          color: "text-blue-600",
        },
        {
          title: "Candidates Registered",
          value: 28,
          icon: Users,
          trend: "+4 this week",
          color: "text-green-600",
        },
        {
          title: "Pending Reviews",
          value: 3,
          icon: Clock,
          trend: "2 urgent",
          color: "text-orange-600",
        },
        {
          title: "Successful Placements",
          value: 7,
          icon: CheckCircle,
          trend: "+2 this month",
          color: "text-purple-600",
        },
      ];
    }
  };

  const quickNavCards = getQuickNavCards();
  const metrics = getMetrics();
  const displayName = name || userName || "User";

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 overflow-auto">
      <div className="container mx-auto px-6 py-8 animate-fade-in max-w-7xl">
        {/* Welcome Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Welcome back, {displayName}!
          </h1>
          <p className="text-xl text-gray-600 capitalize">
            {userRole} Dashboard - Makonis TalentTrack Pro
          </p>
        </div>

        {/* Key Metrics Overview */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Quick Overview
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {metrics.map((metric, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <metric.icon className={`h-8 w-8 ${metric.color}`} />
                  <span className="text-2xl font-bold text-gray-900">
                    {metric.value}
                  </span>
                </div>
                <h3 className="text-sm font-medium text-gray-600 mb-1">
                  {metric.title}
                </h3>
                {metric.trend && (
                  <p className="text-xs text-gray-500">{metric.trend}</p>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Quick Navigation Cards */}
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-4">
            Quick Navigation
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickNavCards.map((card, index) => (
              <div
                key={index}
                onClick={() => navigate(card.href)}
                className="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-all cursor-pointer group hover:scale-105"
              >
                <div className="flex items-center mb-4">
                  <div className={`${card.color} rounded-lg p-3 mr-4`}>
                    <card.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">
                    {card.title}
                  </h3>
                </div>
                <p className="text-gray-600 text-sm mb-4">{card.description}</p>
                <div className="flex items-center text-blue-600 text-sm font-medium">
                  <span>Go to {card.title}</span>
                  <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Dashboard Button */}
        <div className="text-center">
          <button
            onClick={handleGoToDashboard}
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-8 rounded-lg shadow-lg hover:shadow-xl transition-all transform hover:scale-105 flex items-center mx-auto"
          >
            <BarChart3 className="h-6 w-6 mr-2" />
            Go to Main Dashboard
            <ArrowRight className="h-6 w-6 ml-2" />
          </button>
        </div>
      </div>
    </div>
  );
}
